<UserControl
    x:Class="UFU2.Views.NewClient.NActivityDetailView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignWidth="800"
    AutomationProperties.HelpText="Activity detail information form section"
    AutomationProperties.ItemType="Form"
    AutomationProperties.Name="Activity Detail Information Form"
    FlowDirection="RightToLeft"
    Focusable="True"
    IsTabStop="True"
    SnapsToDevicePixels="True"
    UseLayoutRounding="True"
    mc:Ignorable="d">

    <!--  Optimized Grid with performance enhancements  -->


    <!--  Main Content Card  -->

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <materialDesign:Card
            Grid.Row="0"
            Height="174"
            Margin="12,12,12,6"
            Style="{StaticResource ContentCardStyle}">

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>

                <!--  First Row: Activity Details  -->
                <Grid Grid.Row="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="27*" />
                        <ColumnDefinition Width="5*" />
                        <ColumnDefinition Width="8*" />
                        <ColumnDefinition Width="10*" />
                    </Grid.ColumnDefinitions>

                    <!--  النشاط  -->
                    <Grid Grid.Column="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="81" />
                            <ColumnDefinition Width="3*" />
                        </Grid.ColumnDefinitions>

                        <!--  رمز النشاط  -->
                        <TextBox
                            x:Name="ActivityCodeTextBox"
                            Grid.Column="0"
                            materialDesign:HintAssist.Hint="رمز النشاط"
                            materialDesign:TextFieldAssist.CharacterCounterVisibility="Hidden"
                            MaxLength="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource ActivityCodeMaxLengthConverter}}"
                            Style="{StaticResource UnderlineTextBoxStyle}"
                            Text="{Binding ActivityManagement.CurrentActivity.ActivityCode, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                            TextChanged="ActivityCodeTextBox_TextChanged"
                            ToolTip="الرمز الرسمي للنشاط"
                            Visibility="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource ActivityCodeVisibilityConverter}}" />

                        <TextBox
                            x:Name="ActivityDescriptionTextBox"
                            Grid.Column="1"
                            materialDesign:HintAssist.Hint="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource ActivityHintTextConverter}, ConverterParameter=ActivityDescription}"
                            IsReadOnly="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource ActivityDescriptionReadOnlyConverter}}"
                            Style="{StaticResource UnderlineTextBoxStyle}"
                            Text="{Binding ActivityManagement.CurrentActivity.ActivityDescription, Mode=TwoWay, UpdateSourceTrigger=LostFocus}"
                            ToolTip="اسم النشاط التجاري" />

                        <!--  Activity Management Buttons  -->
                        <StackPanel
                            Grid.Column="1"
                            Margin="9,12"
                            HorizontalAlignment="Right"
                            Orientation="Horizontal">

                            <!--  Multiple Activities Management Button (Commercial Activities)  -->
                            <Button
                                x:Name="ManageActivitiesButton"
                                Command="{Binding ManageMultipleActivitiesCommand}"
                                Style="{StaticResource PlusButtonStyle}"
                                ToolTip="إدارة الأنشطة المتعددة"
                                Visibility="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource MultipleActivitiesVisibilityConverter}}">
                                <materialDesign:PackIcon
                                    Width="18"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Kind="Plus" />
                            </Button>

                            <!--  Craft Information Button  -->
                            <Button
                                x:Name="CraftInformationButton"
                                Command="{Binding ShowCraftInformationCommand}"
                                Style="{StaticResource PlusButtonStyle}"
                                ToolTip="معلومات إضافية عن الحرفة"
                                Visibility="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource CraftActivityButtonVisibilityConverter}, ConverterParameter=Information}">
                                <materialDesign:PackIcon
                                    Width="18"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Kind="InformationVariant" />
                            </Button>

                            <!--  Craft Search Button  -->
                            <Button
                                x:Name="CraftSearchButton"
                                Margin="3,0,0,0"
                                Command="{Binding SearchCraftTypesCommand}"
                                Style="{StaticResource PlusButtonStyle}"
                                ToolTip="البحث عن أنواع الحرف"
                                Visibility="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource CraftActivityButtonVisibilityConverter}, ConverterParameter=Search}">
                                <materialDesign:PackIcon
                                    Width="18"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Kind="Magnify" />
                            </Button>
                        </StackPanel>
                    </Grid>

                    <!--  حالة النشاط مع زر التعديل  -->
                    <Grid Grid.Column="1">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <!--  حالة النشاط  -->
                        <ComboBox
                            x:Name="ActivityStatusComboBox"
                            Grid.Row="0"
                            materialDesign:HintAssist.Hint="حالة"
                            ItemsSource="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource ActivityStatusItemsComboBoxConverter}}"
                            SelectedItem="{Binding ActivityManagement.CurrentActivity.ActivityStatus, Mode=TwoWay}"
                            SelectionChanged="ActivityStatusComboBox_SelectionChanged"
                            Style="{StaticResource UnderlineComboBoxStyle}"
                            ToolTip="الحالة الحالية للنشاط" />


                    </Grid>

                    <!--  تاريخ بداية النشاط  -->
                    <TextBox
                        x:Name="ActivityStartDateTextBox"
                        Grid.Column="2"
                        materialDesign:HintAssist.Hint="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource ActivityHintTextConverter}, ConverterParameter=ActivityStartDate}"
                        Style="{StaticResource UnderlineTextBoxStyle}"
                        Text="{Binding ActivityManagement.CurrentActivity.ActivityStartDate, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                        ToolTip="تاريخ بداية ممارسة النشاط" />

                    <!--  رقم السجل التجاري  -->
                    <TextBox
                        x:Name="CommercialRegisterTextBox"
                        Grid.Column="3"
                        materialDesign:HintAssist.Hint="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource ActivityHintTextConverter}, ConverterParameter=CommercialRegister}"
                        Style="{StaticResource UnderlineTextBoxStyle}"
                        Text="{Binding ActivityManagement.CurrentActivity.CommercialRegister, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                        ToolTip="رقم التسجيل في السجل التجاري" />
                </Grid>

                <!--  Second Row: Location Details  -->
                <Grid Grid.Row="1">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="40*" />
                        <ColumnDefinition Width="7*" />
                        <ColumnDefinition Width="10*" />
                        <ColumnDefinition Width="10*" />
                    </Grid.ColumnDefinitions>

                    <!--  عنوان القاعدة التجارية - OPTIMIZED: Changed to LostFocus for better performance  -->
                    <TextBox
                        x:Name="ActivityLocationTextBox"
                        Grid.Column="0"
                        materialDesign:HintAssist.Hint="{Binding ActivityManagement.SelectedActivityType, Converter={StaticResource ActivityHintTextConverter}, ConverterParameter=ActivityLocation}"
                        Style="{StaticResource UnderlineTextBoxStyle}"
                        Text="{Binding ActivityManagement.CurrentActivity.ActivityLocation, Mode=TwoWay, UpdateSourceTrigger=LostFocus}"
                        ToolTip="العنوان الكامل لمكان ممارسة النشاط" />

                    <!--  مصالح الضرائب بولاية  -->
                    <TextBlock
                        Grid.Column="1"
                        VerticalAlignment="Center"
                        Style="{StaticResource BodyTextStyle}"
                        Text="قباضة الضرائب لـ"
                        TextAlignment="Center" />

                    <!--  ولاية  -->
                    <ComboBox
                        x:Name="CpiWilayaComboBox"
                        Grid.Column="2"
                        materialDesign:HintAssist.Hint="ولايــــــة"
                        DisplayMemberPath="DisplayValue"
                        IsReadOnly="True"
                        ItemsSource="{Binding ActivityManagement.CpiWilayas}"
                        SelectedItem="{Binding ActivityManagement.SelectedCpiWilaya, Mode=TwoWay}"
                        Style="{StaticResource UnderlineComboBoxStyle}"
                        ToolTip="اختر الولاية" />

                    <!--  دائرة  -->
                    <ComboBox
                        x:Name="CpiDairaComboBox"
                        Grid.Column="3"
                        materialDesign:HintAssist.Hint="دائرة"
                        DisplayMemberPath="NameAr"
                        ItemsSource="{Binding ActivityManagement.CpiDairas}"
                        SelectedItem="{Binding ActivityManagement.SelectedCpiDaira, Mode=TwoWay}"
                        Style="{StaticResource UnderlineComboBoxStyle}"
                        ToolTip="اختر دائرة قباضة الضرائب" />

                </Grid>

                <!--  Third Row: Financial and Status Information  -->
                <Grid Grid.Row="2">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*" />
                        <ColumnDefinition Width="2*" />
                        <ColumnDefinition Width="1*" />
                    </Grid.ColumnDefinitions>

                    <!--  رقم التعريف الجبائي (NIF) - OPTIMIZED: Changed to LostFocus for better performance  -->
                    <TextBox
                        x:Name="NifNumberTextBox"
                        Grid.Column="0"
                        materialDesign:HintAssist.Hint="رقم التعريف الجبائي (NIF)"
                        Style="{StaticResource UnderlineTextBoxStyle}"
                        Text="{Binding ActivityManagement.CurrentActivity.NifNumber, Mode=TwoWay, UpdateSourceTrigger=LostFocus}" />

                    <!--  رقم التعريف الاحصائي (NIS) - OPTIMIZED: Changed to LostFocus for better performance  -->
                    <TextBox
                        x:Name="NisNumberTextBox"
                        Grid.Column="1"
                        materialDesign:HintAssist.Hint="رقم التعريف الاحصائي (NIS)"
                        Style="{StaticResource UnderlineTextBoxStyle}"
                        Text="{Binding ActivityManagement.CurrentActivity.NisNumber, Mode=TwoWay, UpdateSourceTrigger=LostFocus}" />

                    <!--  رقم المادة - OPTIMIZED: Changed to LostFocus for better performance  -->
                    <TextBox
                        x:Name="ArtNumberTextBox"
                        Grid.Column="2"
                        materialDesign:HintAssist.Hint="رقم المادة (ART)"
                        Style="{StaticResource UnderlineTextBoxStyle}"
                        Text="{Binding ActivityManagement.CurrentActivity.ArtNumber, Mode=TwoWay, UpdateSourceTrigger=LostFocus}" />
                </Grid>
            </Grid>
        </materialDesign:Card>
        <Grid Grid.Row="1" Visibility="{Binding ActivityManagement.CurrentActivity.ActivityStatus, Converter={StaticResource ActivityStatusVisibilityConverter}}">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="4*" />
            </Grid.ColumnDefinitions>
            <materialDesign:Card
                Grid.Column="0"
                Height="36"
                Margin="12,0,3,12"
                Padding="12,0"
                Style="{StaticResource ContentCardStyle}">
                <DockPanel LastChildFill="True">
                    <TextBlock
                        DockPanel.Dock="Left"
                        Style="{DynamicResource LabelTextStyle}"
                        Text="تاريخ تحديث : " />
                    <TextBlock
                        x:Name="ActivityUpdatesDate"
                        Style="{DynamicResource LabelTextStyle}"
                        Text="{Binding ActivityManagement.CurrentActivity.ActivityUpdateDate, Mode=OneWay}"
                        TextAlignment="Center" />
                </DockPanel>
            </materialDesign:Card>

            <materialDesign:Card
                Grid.Column="1"
                Height="36"
                Margin="3,0,12,12"
                Padding="12,0"
                Style="{StaticResource ContentCardStyle}">

                <DockPanel LastChildFill="True">
                    <TextBlock
                        DockPanel.Dock="Left"
                        Style="{DynamicResource LabelTextStyle}"
                        Text="ملاحظـــة : " />

                    <Button
                        x:Name="EditStatusButton"
                        Grid.Column="2"
                        VerticalAlignment="Center"
                        Command="{Binding EditStatusCommand}"
                        DockPanel.Dock="Right"
                        Style="{StaticResource PlusButtonStyle}"
                        ToolTip="تعديل تاريخ وملاحظات تحديث الحالة">
                        <materialDesign:PackIcon
                            Width="18"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Kind="NoteEditOutline" />
                    </Button>

                    <TextBlock
                        x:Name="ActivityUpdatesNote"
                        Margin="7,0,0,0"
                        Style="{DynamicResource LabelTextStyle}"
                        Text="{Binding ActivityManagement.CurrentActivity.ActivityUpdateNote, Mode=OneWay}" />

                </DockPanel>
            </materialDesign:Card>
        </Grid>
    </Grid>

</UserControl>
