=== UFU2 Application Session Started at 2025-08-09 14:24:20 ===
[2025-08-09 14:24:20.576]  	[INFO]		[LoggingService]	Log level set to Debug
[2025-08-09 14:24:20.583]  	[INFO]		[App]	UFU2 Application starting up
[2025-08-09 14:24:20.589]  	[DEBUG]		[ServiceLocator]	Initializing ServiceLocator
[2025-08-09 14:24:20.593]  	[DEBUG]		[ServiceLocator]	Service IToastService registered
[2025-08-09 14:24:20.633]  	[DEBUG]		[ServiceLocator]	Service ValidationService registered
[2025-08-09 14:24:20.637]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ValidationService
[2025-08-09 14:24:20.642]  	[DEBUG]		[WindowChromeService]	Initializing WindowChromeService
[2025-08-09 14:24:20.649]  	[DEBUG]		[WindowChromeService]	Updating window chrome theme to: Dark
[2025-08-09 14:24:20.655]  	[WARN]		[ThemeManager]	Theme color 'ButtonHoverBackground' not found in current theme
[2025-08-09 14:24:20.660]  	[WARN]		[ThemeManager]	Theme color 'ButtonPressedBackground' not found in current theme
[2025-08-09 14:24:20.666]  	[DEBUG]		[WindowChromeService]	Current theme updated for Dark
[2025-08-09 14:24:20.671]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarBackground: #E61D1F20
[2025-08-09 14:24:20.678]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarForeground: #FFFFFFFF
[2025-08-09 14:24:20.685]  	[DEBUG]		[WindowChromeService]	Theme updated - WindowBorderBrush: #E6353A3E
[2025-08-09 14:24:20.689]  	[DEBUG]		[WindowChromeService]	Window chrome theme updated successfully to Dark
[2025-08-09 14:24:20.694]  	[DEBUG]		[WindowChromeService]	Subscribed to ThemeManager events
[2025-08-09 14:24:20.699]  	[DEBUG]		[WindowChromeService]	WindowChromeService initialized successfully
[2025-08-09 14:24:20.704]  	[DEBUG]		[ServiceLocator]	Service IWindowChromeService registered
[2025-08-09 14:24:20.716]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService initialized with smart batching and UI thread monitoring
[2025-08-09 14:24:20.720]  	[DEBUG]		[ServiceLocator]	Service DispatcherOptimizationService registered
[2025-08-09 14:24:20.725]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring started
[2025-08-09 14:24:20.730]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService initialized
[2025-08-09 14:24:20.734]  	[DEBUG]		[ServiceLocator]	Service UIResponsivenessMonitoringService registered
[2025-08-09 14:24:20.740]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService initialized
[2025-08-09 14:24:20.747]  	[DEBUG]		[ServiceLocator]	Service BackgroundViewInitializationService registered
[2025-08-09 14:24:20.753]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService initialized
[2025-08-09 14:24:20.757]  	[DEBUG]		[ServiceLocator]	Service ViewMemoryOptimizationService registered
[2025-08-09 14:24:20.762]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService initialized
[2025-08-09 14:24:20.766]  	[DEBUG]		[ServiceLocator]	Service ViewLoadingMonitoringService registered
[2025-08-09 14:24:20.771]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService initialized
[2025-08-09 14:24:20.776]  	[DEBUG]		[ServiceLocator]	Service PerformanceDashboardService registered
[2025-08-09 14:24:20.782]  	[INFO]		[ServiceLocator]	Initializing Phase 2D memory management services
[2025-08-09 14:24:20.789]  	[INFO]		[ResourceManager]	ResourceManager initialized with automatic cleanup and memory monitoring
[2025-08-09 14:24:20.798]  	[DEBUG]		[ServiceLocator]	Service ResourceManager registered
[2025-08-09 14:24:20.806]  	[DEBUG]		[ServiceLocator]	ResourceManager registered successfully
[2025-08-09 14:24:20.812]  	[INFO]		[WeakEventManager]	WeakEventManager initialized with automatic cleanup
[2025-08-09 14:24:20.817]  	[DEBUG]		[ServiceLocator]	Service WeakEventManager registered
[2025-08-09 14:24:20.822]  	[DEBUG]		[ServiceLocator]	WeakEventManager registered successfully
[2025-08-09 14:24:20.843]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 117.14MB working set
[2025-08-09 14:24:20.848]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService initialized with automatic monitoring
[2025-08-09 14:24:20.853]  	[DEBUG]		[ServiceLocator]	Service MemoryLeakDetectionService registered
[2025-08-09 14:24:20.859]  	[DEBUG]		[ServiceLocator]	MemoryLeakDetectionService registered successfully
[2025-08-09 14:24:20.864]  	[INFO]		[ServiceLocator]	Phase 2D memory management services initialized successfully
[2025-08-09 14:24:20.869]  	[DEBUG]		[ServiceLocator]	ServiceLocator initialized successfully with comprehensive UI optimization, background processing, performance dashboard, and memory management services
[2025-08-09 14:24:20.878]  	[DEBUG]		[ThemeManager]	Initializing ThemeManager
[2025-08-09 14:24:20.910]  	[DEBUG]		[ThemeManager]	Theme resource dictionaries loaded successfully
[2025-08-09 14:24:20.916]  	[DEBUG]		[ThemeManager]	Found MaterialDesign BundledTheme in application resources
[2025-08-09 14:24:20.921]  	[DEBUG]		[ThemeManager]	ThemeManager initialized with Dark theme
[2025-08-09 14:24:21.217]  	[DEBUG]		[MainWindow]	Initializing custom window chrome for MainWindow
[2025-08-09 14:24:21.225]  	[DEBUG]		[MainWindow]	Getting WindowChromeService from ServiceLocator
[2025-08-09 14:24:21.237]  	[DEBUG]		[MainWindow]	WindowChromeService retrieved successfully
[2025-08-09 14:24:21.242]  	[DEBUG]		[MainWindow]	Creating CustomWindowChromeViewModel
[2025-08-09 14:24:21.277]  	[DEBUG]		[ResourceManager]	Registered resource: CustomWindowChromeViewModel_51489795_638903426612748402 (BaseViewModel) for CustomWindowChromeViewModel
[2025-08-09 14:24:21.281]  	[DEBUG]		[CustomWindowChromeViewModel]	BaseViewModel memory management initialized for CustomWindowChromeViewModel
[2025-08-09 14:24:21.287]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:24:21.292]  	[DEBUG]		[CustomWindowChromeViewModel]	Initializing CustomWindowChromeViewModel
[2025-08-09 14:24:21.307]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU2
[2025-08-09 14:24:21.315]  	[DEBUG]		[CustomWindowChromeViewModel]	Properties initialized with default values
[2025-08-09 14:24:21.320]  	[DEBUG]		[CustomWindowChromeViewModel]	Window control commands initialized successfully
[2025-08-09 14:24:21.326]  	[DEBUG]		[CustomWindowChromeViewModel]	Subscribed to theme change events
[2025-08-09 14:24:21.332]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarBackground' not found or invalid type
[2025-08-09 14:24:21.337]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarForeground' not found or invalid type
[2025-08-09 14:24:21.343]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_WindowBorder' not found or invalid type
[2025-08-09 14:24:21.349]  	[DEBUG]		[CustomWindowChromeViewModel]	Theme properties updated for Dark theme
[2025-08-09 14:24:21.355]  	[DEBUG]		[CustomWindowChromeViewModel]	Applied current theme: Dark
[2025-08-09 14:24:21.362]  	[DEBUG]		[CustomWindowChromeViewModel]	Updating RTL layout for culture: en-US, IsRTL: False
[2025-08-09 14:24:21.369]  	[DEBUG]		[CustomWindowChromeViewModel]	RTL layout updated - FlowDirection: LeftToRight, TitleAlignment: Left, ControlsAlignment: Right
[2025-08-09 14:24:21.374]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel initialized successfully
[2025-08-09 14:24:21.381]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel created successfully
[2025-08-09 14:24:21.386]  	[DEBUG]		[MainWindow]	Setting window properties
[2025-08-09 14:24:21.392]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU Client Management
[2025-08-09 14:24:21.397]  	[DEBUG]		[CustomWindowChromeViewModel]	Window icon changed
[2025-08-09 14:24:21.403]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-09 14:24:21.409]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Normal
[2025-08-09 14:24:21.414]  	[DEBUG]		[MainWindow]	Window properties set successfully
[2025-08-09 14:24:21.435]  	[DEBUG]		[MainWindow]	Setting DataContext
[2025-08-09 14:24:21.440]  	[DEBUG]		[MainWindow]	DataContext set successfully
[2025-08-09 14:24:21.445]  	[DEBUG]		[MainWindow]	Applying WindowChrome
[2025-08-09 14:24:21.454]  	[DEBUG]		[WindowChromeService]	Configuring window chrome for MainWindow with ViewModel CustomWindowChromeViewModel
[2025-08-09 14:24:21.467]  	[DEBUG]		[WindowChromeService]	Applying custom chrome to window: MainWindow (Title: UFU Client Management)
[2025-08-09 14:24:21.477]  	[DEBUG]		[WindowChromeService]	Validating window for chrome application: MainWindow
[2025-08-09 14:24:21.489]  	[WARN]		[WindowChromeService]	Window is not loaded, cannot apply chrome
[2025-08-09 14:24:21.505]  	[WARN]		[WindowChromeService]	Window validation failed for chrome application: MainWindow
[2025-08-09 14:24:21.513]  	[DEBUG]		[WindowChromeService]	Applying fallback chrome to window: MainWindow
[2025-08-09 14:24:21.520]  	[DEBUG]		[WindowChromeService]	Existing WindowChrome removed
[2025-08-09 14:24:21.526]  	[DEBUG]		[WindowChromeService]	Creating fallback WindowChrome with safe defaults
[2025-08-09 14:24:21.532]  	[DEBUG]		[WindowChromeService]	Fallback WindowChrome created successfully
[2025-08-09 14:24:21.537]  	[DEBUG]		[WindowChromeService]	Fallback chrome applied successfully
[2025-08-09 14:24:21.543]  	[DEBUG]		[WindowChromeService]	Custom chrome applied during configuration
[2025-08-09 14:24:21.549]  	[DEBUG]		[WindowChromeService]	Ensuring native window behaviors for: UFU Client Management
[2025-08-09 14:24:21.556]  	[DEBUG]		[WindowChromeService]	Ensuring optimal WindowChrome configuration for native behaviors
[2025-08-09 14:24:21.563]  	[DEBUG]		[WindowChromeService]	WindowChrome configuration optimized for native behaviors
[2025-08-09 14:24:21.569]  	[DEBUG]		[WindowChromeService]	Validating Aero Snap functionality for window: UFU Client Management
[2025-08-09 14:24:21.574]  	[DEBUG]		[WindowChromeService]	Aero Snap functionality validation completed successfully
[2025-08-09 14:24:21.580]  	[DEBUG]		[WindowChromeService]	Validating window resizing functionality for: UFU Client Management
[2025-08-09 14:24:21.589]  	[DEBUG]		[WindowChromeService]	Resize border thickness is adequate: 8,8,8,8
[2025-08-09 14:24:21.598]  	[DEBUG]		[WindowChromeService]	Window resizing validation completed successfully
[2025-08-09 14:24:21.604]  	[DEBUG]		[WindowChromeService]	Native window behaviors configuration completed
[2025-08-09 14:24:21.612]  	[DEBUG]		[WindowChromeService]	Native window behaviors ensured
[2025-08-09 14:24:21.620]  	[DEBUG]		[WindowChromeService]	Validating window configuration
[2025-08-09 14:24:21.627]  	[DEBUG]		[WindowChromeService]	Window configuration validation passed
[2025-08-09 14:24:21.638]  	[DEBUG]		[WindowChromeService]	Window chrome configuration completed successfully
[2025-08-09 14:24:21.647]  	[DEBUG]		[MainWindow]	WindowChrome applied successfully
[2025-08-09 14:24:21.656]  	[DEBUG]		[MainWindow]	Subscribed to window StateChanged event
[2025-08-09 14:24:21.671]  	[DEBUG]		[MainWindow]	Custom window chrome initialized successfully
[2025-08-09 14:24:21.678]  	[DEBUG]		[MainWindow]	Initializing keyboard support and accessibility features
[2025-08-09 14:24:21.684]  	[DEBUG]		[MainWindow]	Keyboard support and accessibility features initialized successfully
[2025-08-09 14:24:21.691]  	[DEBUG]		[ToastService]	Subscribed to theme change events
[2025-08-09 14:24:21.698]  	[DEBUG]		[ToastService]	ToastService initialized in desktop-only mode with position: BottomRight
[2025-08-09 14:24:21.704]  	[DEBUG]		[MainWindow]	ToastService initialized successfully in desktop-only mode
[2025-08-09 14:24:21.714]  	[WARN]		[ThemeManager]	Color 'PrimaryColor' not found in theme dictionary
[2025-08-09 14:24:21.720]  	[WARN]		[ThemeManager]	Color 'SecondaryColor' not found in theme dictionary
[2025-08-09 14:24:21.728]  	[DEBUG]		[ThemeManager]	Custom MaterialDesign colors applied for Dark theme
[2025-08-09 14:24:21.735]  	[DEBUG]		[ThemeManager]	MaterialDesign theme updated to Dark with UFU2 custom colors
[2025-08-09 14:24:21.747]  	[DEBUG]		[ThemeManager]	Removed 1 existing theme dictionaries
[2025-08-09 14:24:21.762]  	[DEBUG]		[ThemeManager]	Custom theme resources updated to Dark
[2025-08-09 14:24:21.768]  	[DEBUG]		[ThemeManager]	Theme application completed for Dark
[2025-08-09 14:24:21.776]  	[DEBUG]		[ThemeManager]	ThemeManager.Initialization completed in 898ms
[2025-08-09 14:24:21.785]  	[DEBUG]		[App]	ThemeManager initialized successfully
[2025-08-09 14:24:21.796]  	[DEBUG]		[ServiceLocator]	Initializing three-database architecture services
[2025-08-09 14:24:21.830]  	[INFO]		[DatabaseService]	DatabaseService initialized for ClientData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db and connection pool (max: 10)
[2025-08-09 14:24:21.852]  	[DEBUG]		[ServiceLocator]	Service DatabaseService registered
[2025-08-09 14:24:21.871]  	[INFO]		[DatabaseService]	DatabaseService initialized for ReferenceData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\APP_Database.db and connection pool (max: 10)
[2025-08-09 14:24:21.883]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceDatabase' registered with type DatabaseService
[2025-08-09 14:24:21.890]  	[INFO]		[DatabaseService]	DatabaseService initialized for ArchiveData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\Archive_Database.db and connection pool (max: 10)
[2025-08-09 14:24:21.901]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveDatabase' registered with type DatabaseService
[2025-08-09 14:24:21.911]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 14:24:21.935]  	[DEBUG]		[ServiceLocator]	Service DatabaseMigrationService registered
[2025-08-09 14:24:21.956]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 14:24:21.973]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceMigrationService' registered with type DatabaseMigrationService
[2025-08-09 14:24:21.980]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 14:24:21.992]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveMigrationService' registered with type DatabaseMigrationService
[2025-08-09 14:24:22.016]  	[INFO]		[ServiceLocator]	Initializing client database schema
[2025-08-09 14:24:22.034]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 14:24:22.125]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:24:22.125]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:24:22.133]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:24:22.127]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:24:22.137]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:24:22.145]  	[DEBUG]		[DatabaseService]	Created new pooled database connection
[2025-08-09 14:24:22.156]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:24:22.148]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:24:22.164]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 14:24:22.167]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 14:24:22.172]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 14:24:22.152]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 14:24:22.177]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 14:24:22.195]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 14:24:22.202]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 14:24:22.210]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 14:24:22.216]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 14:24:22.222]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 14:24:22.229]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 14:24:22.247]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 14:24:22.255]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 14:24:22.280]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 14:24:22.289]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:24:22.301]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 14:24:22.346]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 14:24:22.352]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 14:24:22.358]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 14:24:22.363]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 14:24:22.368]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 14:24:22.375]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 14:24:22.382]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 14:24:22.387]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 14:24:22.393]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 14:24:22.399]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 14:24:22.405]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 14:24:22.411]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 14:24:22.418]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 14:24:22.424]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 14:24:22.430]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 14:24:22.435]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 14:24:22.441]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 14:24:22.448]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 14:24:22.453]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 14:24:22.460]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 14:24:22.468]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 14:24:22.474]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 14:24:22.480]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 14:24:22.487]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 14:24:22.494]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 14:24:22.500]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 14:24:22.506]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 14:24:22.517]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 14:24:22.525]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:22.533]  	[INFO]		[ServiceLocator]	Initializing reference database schema
[2025-08-09 14:24:22.539]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 14:24:22.545]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:22.551]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 14:24:22.556]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 14:24:22.563]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 14:24:22.568]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 14:24:22.574]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 14:24:22.579]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 14:24:22.585]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 14:24:22.590]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 14:24:22.596]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 14:24:22.601]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 14:24:22.606]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 14:24:22.613]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:24:22.619]  	[DEBUG]		[DatabaseMigrationService]	Validating 5 required tables
[2025-08-09 14:24:22.626]  	[DEBUG]		[DatabaseMigrationService]	Table 'ActivityTypeBase' exists
[2025-08-09 14:24:22.634]  	[DEBUG]		[DatabaseMigrationService]	Table 'CraftTypeBase' exists
[2025-08-09 14:24:22.639]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiWilayas' exists
[2025-08-09 14:24:22.658]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiDairas' exists
[2025-08-09 14:24:22.668]  	[DEBUG]		[DatabaseMigrationService]	Table 'ReferenceSchemaVersion' exists
[2025-08-09 14:24:22.676]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 critical indexes for ReferenceData database
[2025-08-09 14:24:22.683]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_activity_type_code' is missing
[2025-08-09 14:24:22.689]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_craft_type_code' is missing
[2025-08-09 14:24:22.696]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_wilayas_code' is missing
[2025-08-09 14:24:22.702]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_dairas_code' is missing
[2025-08-09 14:24:22.708]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 14:24:22.716]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 14:24:22.721]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ReferenceData database
[2025-08-09 14:24:22.730]  	[DEBUG]		[DatabaseMigrationService]	Validating reference data integrity
[2025-08-09 14:24:22.739]  	[DEBUG]		[DatabaseMigrationService]	Reference data integrity validation completed
[2025-08-09 14:24:22.762]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ReferenceData database
[2025-08-09 14:24:22.780]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 14:24:22.787]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 14:24:22.794]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:22.801]  	[INFO]		[ServiceLocator]	Initializing archive database schema
[2025-08-09 14:24:22.807]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 14:24:22.815]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:22.821]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 14:24:22.828]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 14:24:22.834]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 14:24:22.840]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 14:24:22.847]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 14:24:22.853]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 14:24:22.859]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 14:24:22.866]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 14:24:22.872]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 14:24:22.879]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 14:24:22.886]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 14:24:22.894]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:24:22.902]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 required tables
[2025-08-09 14:24:22.909]  	[DEBUG]		[DatabaseMigrationService]	Table 'AddedEntities' exists
[2025-08-09 14:24:22.917]  	[DEBUG]		[DatabaseMigrationService]	Table 'UpdatedEntities' exists
[2025-08-09 14:24:22.923]  	[DEBUG]		[DatabaseMigrationService]	Table 'DeletedEntities' exists
[2025-08-09 14:24:22.935]  	[DEBUG]		[DatabaseMigrationService]	Table 'ArchiveSchemaVersion' exists
[2025-08-09 14:24:22.956]  	[DEBUG]		[DatabaseMigrationService]	Validating 3 critical indexes for ArchiveData database
[2025-08-09 14:24:22.965]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_added_entities_type_id' is missing
[2025-08-09 14:24:22.971]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_updated_entities_type_id' is missing
[2025-08-09 14:24:22.980]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_deleted_entities_type_id' is missing
[2025-08-09 14:24:22.986]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 14:24:22.995]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 14:24:23.002]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ArchiveData database
[2025-08-09 14:24:23.016]  	[DEBUG]		[DatabaseMigrationService]	Validating archive data integrity
[2025-08-09 14:24:23.034]  	[DEBUG]		[DatabaseMigrationService]	Archive data integrity validation completed
[2025-08-09 14:24:23.040]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ArchiveData database
[2025-08-09 14:24:23.050]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 14:24:23.057]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 14:24:23.066]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:23.072]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 14:24:23.079]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:24:23.086]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 14:24:23.092]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 14:24:23.099]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 14:24:23.104]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 14:24:23.110]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 14:24:23.116]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 14:24:23.121]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 14:24:23.128]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 14:24:23.134]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 14:24:23.140]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 14:24:23.167]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 14:24:23.173]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 14:24:23.180]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 14:24:23.186]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 14:24:23.193]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 14:24:23.199]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 14:24:23.205]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 14:24:23.211]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 14:24:23.218]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 14:24:23.224]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 14:24:23.231]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 14:24:23.238]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 14:24:23.245]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 14:24:23.251]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 14:24:23.257]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 14:24:23.264]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 14:24:23.270]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 14:24:23.281]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 14:24:23.287]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService initialized
[2025-08-09 14:24:23.294]  	[DEBUG]		[ServiceLocator]	Service UIDGenerationService registered
[2025-08-09 14:24:23.300]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService initialized
[2025-08-09 14:24:23.306]  	[DEBUG]		[ServiceLocator]	Service ArchiveDatabaseService registered
[2025-08-09 14:24:23.313]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService initialized with audit logging support
[2025-08-09 14:24:23.320]  	[DEBUG]		[ServiceLocator]	Service ClientDatabaseService registered
[2025-08-09 14:24:23.328]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService initialized
[2025-08-09 14:24:23.336]  	[DEBUG]		[ServiceLocator]	Service DatabasePerformanceMonitoringService registered
[2025-08-09 14:24:23.383]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService initialized with performance monitoring
[2025-08-09 14:24:23.429]  	[DEBUG]		[ServiceLocator]	Service EnhancedDatabaseService registered
[2025-08-09 14:24:23.465]  	[INFO]		[DatabaseSchemaValidator]	DatabaseSchemaValidator initialized
[2025-08-09 14:24:23.472]  	[DEBUG]		[ServiceLocator]	Service DatabaseSchemaValidator registered
[2025-08-09 14:24:23.479]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBaseService initialized with reference database
[2025-08-09 14:24:23.486]  	[DEBUG]		[ServiceLocator]	Service ActivityTypeBaseService registered
[2025-08-09 14:24:23.493]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ActivityTypeBaseService
[2025-08-09 14:24:23.500]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBaseService initialized with reference database
[2025-08-09 14:24:23.506]  	[DEBUG]		[ServiceLocator]	Service CraftTypeBaseService registered
[2025-08-09 14:24:23.513]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CraftTypeBaseService
[2025-08-09 14:24:23.521]  	[DEBUG]		[CpiLocationService]	CpiLocationService initialized with reference database
[2025-08-09 14:24:23.528]  	[DEBUG]		[ServiceLocator]	Service CpiLocationService registered
[2025-08-09 14:24:23.537]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CpiLocationService
[2025-08-09 14:24:23.546]  	[DEBUG]		[ArchiveDatabaseService]	Creating archive database tables
[2025-08-09 14:24:23.555]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:24:23.575]  	[INFO]		[ArchiveDatabaseService]	Archive database tables created successfully
[2025-08-09 14:24:23.582]  	[INFO]		[ServiceLocator]	Initializing Arabic search services with exact prefix matching
[2025-08-09 14:24:23.588]  	[INFO]		[ArabicTextAnalyzer]	ArabicTextAnalyzer initialized with word frequency-based analysis
[2025-08-09 14:24:23.597]  	[DEBUG]		[ServiceLocator]	Service IArabicTextAnalyzer registered
[2025-08-09 14:24:23.603]  	[INFO]		[WordFrequencySearchService]	WordFrequencySearchService initialized with Arabic text analysis
[2025-08-09 14:24:23.610]  	[DEBUG]		[ServiceLocator]	Service WordFrequencySearchService registered
[2025-08-09 14:24:23.630]  	[INFO]		[ServiceLocator]	Arabic search services with exact prefix matching initialized successfully
[2025-08-09 14:24:23.638]  	[DEBUG]		[ServiceLocator]	Service FileCheckBusinessRuleService registered
[2025-08-09 14:24:23.649]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: FileCheckBusinessRuleService
[2025-08-09 14:24:23.657]  	[DEBUG]		[ServiceLocator]	Service ClientValidationService registered
[2025-08-09 14:24:23.664]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService initialized with base directory: C:\Users\<USER>\AppData\Roaming\UFU2\Clients
[2025-08-09 14:24:23.670]  	[DEBUG]		[ServiceLocator]	Service ClientFolderManagementService registered
[2025-08-09 14:24:23.677]  	[DEBUG]		[ServiceLocator]	Service DuplicateClientDetectionService registered
[2025-08-09 14:24:23.685]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:23.690]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBase table validation completed successfully
[2025-08-09 14:24:23.697]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:23.705]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:24:23.712]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:23.718]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBase table validation completed successfully
[2025-08-09 14:24:23.723]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:23.733]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:24:23.739]  	[INFO]		[ServiceLocator]	Seeding CPI location data from embedded resource
[2025-08-09 14:24:23.757]  	[INFO]		[CpiLocationService]	Starting CPI location data seeding from embedded JSON resource
[2025-08-09 14:24:23.841]  	[INFO]		[CpiLocationService]	Starting to seed 58 wilayas and 281 dairas
[2025-08-09 14:24:23.853]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:24:23.899]  	[DEBUG]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 14:24:23.905]  	[INFO]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 14:24:23.914]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:24:23.940]  	[DEBUG]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 14:24:23.947]  	[INFO]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 14:24:23.953]  	[DEBUG]		[CpiLocationService]	All caches cleared
[2025-08-09 14:24:23.960]  	[INFO]		[CpiLocationService]	CPI location data seeding completed successfully
[2025-08-09 14:24:23.968]  	[INFO]		[CpiLocationService]	Displaying user success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 14:24:23.982]  	[DEBUG]		[ToastService]	Theme resources applied to desktop toast window
[2025-08-09 14:24:24.352]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:24:24.444]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 14:24:24.452]  	[DEBUG]		[ToastService]	Desktop toast notifier initialized with theme support
[2025-08-09 14:24:24.467]  	[DEBUG]		[ToastService]	Displaying Success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 14:24:24.622]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل بنجاح
[2025-08-09 14:24:24.640]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 14:24:24.653]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل بنجاح
[2025-08-09 14:24:24.665]  	[INFO]		[ServiceLocator]	Successfully seeded CPI location data - Wilayas: 58, Dairas: 281
[2025-08-09 14:24:24.674]  	[DEBUG]		[DatabaseMigrationService]	Collecting database statistics for ClientData database
[2025-08-09 14:24:24.681]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:24:24.707]  	[DEBUG]		[DatabaseMigrationService]	Client database stats: 1 clients, 0 activities
[2025-08-09 14:24:24.735]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: MainWindow_CommonData (Priority: Low)
[2025-08-09 14:24:24.848]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:24.955]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: MainWindow (MainWindow)
[2025-08-09 14:24:25.003]  	[DEBUG]		[DatabaseMigrationService]	Database stats collected for ClientData: 0.27 MB MB size
[2025-08-09 14:24:25.011]  	[INFO]		[MainWindow]	Background services initialized successfully
[2025-08-09 14:24:24.968]  	[DEBUG]		[BackgroundViewInitializationService]	Background processing status - Active: 1, Queue: 0, Avg: 0ms
[2025-08-09 14:24:25.115]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:24:25.088]  	[INFO]		[ServiceLocator]	Three-database architecture initialized - Client DB Version: 5, Tables: 12, Indexes: 52
[2025-08-09 14:24:25.171]  	[DEBUG]		[ActivityTypeBaseService]	Cached 1028 activity types (Cache hits: 0, misses: 1)
[2025-08-09 14:24:25.184]  	[DEBUG]		[ServiceLocator]	All required database services are registered
[2025-08-09 14:24:25.223]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:25.283]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService initialized with health monitoring and coordinated cleanup
[2025-08-09 14:24:25.308]  	[INFO]		[MainWindow]	Background common data preloading completed
[2025-08-09 14:24:25.343]  	[DEBUG]		[ServiceLocator]	Service CacheCoordinatorService registered
[2025-08-09 14:24:25.351]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: MainWindow_CommonData in 516ms
[2025-08-09 14:24:25.356]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ValidationService
[2025-08-09 14:24:25.368]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ActivityTypeBaseService
[2025-08-09 14:24:25.376]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CraftTypeBaseService
[2025-08-09 14:24:25.384]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CpiLocationService
[2025-08-09 14:24:25.396]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: FileCheckBusinessRuleService
[2025-08-09 14:24:25.403]  	[INFO]		[ServiceLocator]	Cache coordinator initialized with 5 cacheable services
[2025-08-09 14:24:25.412]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService initialized with monitoring, cleanup, and statistics collection
[2025-08-09 14:24:25.420]  	[DEBUG]		[ServiceLocator]	Service CacheMonitoringService registered
[2025-08-09 14:24:25.434]  	[INFO]		[ServiceLocator]	Cache monitoring service initialized
[2025-08-09 14:24:25.446]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler initialized with memory monitoring
[2025-08-09 14:24:25.453]  	[DEBUG]		[ServiceLocator]	Service MemoryPressureHandler registered
[2025-08-09 14:24:25.460]  	[INFO]		[ServiceLocator]	Memory pressure handler initialized
[2025-08-09 14:24:25.471]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 5 services
[2025-08-09 14:24:25.480]  	[DEBUG]		[ActivityTypeBaseService]	Starting cache warmup for ActivityTypeBaseService
[2025-08-09 14:24:25.489]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 1, misses: 1)
[2025-08-09 14:24:25.525]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:25.551]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'تجاري' returned 0 results (Cache hits: 0, misses: 1)
[2025-08-09 14:24:25.558]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:25.565]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:25.572]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'حرفي' returned 0 results (Cache hits: 0, misses: 2)
[2025-08-09 14:24:25.578]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:25.586]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:25.597]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'مهني' returned 0 results (Cache hits: 0, misses: 3)
[2025-08-09 14:24:25.606]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:25.617]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:25.637]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Commercial' returned 0 results (Cache hits: 0, misses: 4)
[2025-08-09 14:24:25.679]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:25.694]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:25.708]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Craft' returned 0 results (Cache hits: 0, misses: 5)
[2025-08-09 14:24:25.727]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:25.734]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:25.743]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Professional' returned 0 results (Cache hits: 0, misses: 6)
[2025-08-09 14:24:25.753]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:25.776]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 0, Data cache hits: 1
[2025-08-09 14:24:25.826]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 345ms
[2025-08-09 14:24:25.841]  	[DEBUG]		[ValidationService]	Starting cache warmup for ValidationService
[2025-08-09 14:24:25.871]  	[INFO]		[ValidationService]	Cache warmup completed. Cache hits: 0, Cache misses: 2
[2025-08-09 14:24:25.888]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ValidationService in 49ms
[2025-08-09 14:24:25.899]  	[DEBUG]		[FileCheckBusinessRuleService]	Starting cache warmup for FileCheckBusinessRuleService
[2025-08-09 14:24:25.912]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 0, Cache misses: 8
[2025-08-09 14:24:25.918]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 20ms
[2025-08-09 14:24:25.927]  	[DEBUG]		[CpiLocationService]	Starting cache warmup
[2025-08-09 14:24:25.937]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:24:25.946]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 58 wilayas
[2025-08-09 14:24:25.954]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:24:25.964]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 23 dairas for wilaya 16
[2025-08-09 14:24:25.973]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:24:25.985]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 10 dairas for wilaya 31
[2025-08-09 14:24:25.995]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:24:26.003]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 3 dairas for wilaya 25
[2025-08-09 14:24:26.011]  	[DEBUG]		[CpiLocationService]	Cache warmup completed
[2025-08-09 14:24:26.018]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 92ms
[2025-08-09 14:24:26.026]  	[DEBUG]		[CraftTypeBaseService]	Starting cache warmup for CraftTypeBaseService
[2025-08-09 14:24:26.035]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:26.050]  	[DEBUG]		[CraftTypeBaseService]	Cached 337 craft types (Cache hits: 0, misses: 1)
[2025-08-09 14:24:26.059]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:26.081]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:26.089]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تجاري' (Cache hits: 0, misses: 1)
[2025-08-09 14:24:26.097]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:26.109]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:26.119]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'حرفي' (Cache hits: 0, misses: 2)
[2025-08-09 14:24:26.127]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:26.135]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:26.143]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'مهني' (Cache hits: 0, misses: 3)
[2025-08-09 14:24:26.151]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:26.165]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:26.173]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'صناعة' (Cache hits: 0, misses: 4)
[2025-08-09 14:24:26.180]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:26.189]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:26.199]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تقليدي' (Cache hits: 0, misses: 5)
[2025-08-09 14:24:26.206]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:26.215]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:26.222]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Commercial' (Cache hits: 0, misses: 6)
[2025-08-09 14:24:26.231]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:26.238]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:26.244]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Craft' (Cache hits: 0, misses: 7)
[2025-08-09 14:24:26.251]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:26.258]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:26.265]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Professional' (Cache hits: 0, misses: 8)
[2025-08-09 14:24:26.272]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:26.283]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:24:26.290]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Traditional' (Cache hits: 0, misses: 9)
[2025-08-09 14:24:26.298]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:24:26.304]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-09 14:24:26.312]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 288ms
[2025-08-09 14:24:26.325]  	[INFO]		[ServiceLocator]	=== Service Registration Status ===
[2025-08-09 14:24:26.343]  	[INFO]		[ServiceLocator]	DatabaseService: ✓ Registered
[2025-08-09 14:24:26.353]  	[INFO]		[ServiceLocator]	DatabaseMigrationService: ✓ Registered
[2025-08-09 14:24:26.361]  	[INFO]		[ServiceLocator]	UIDGenerationService: ✓ Registered
[2025-08-09 14:24:26.367]  	[INFO]		[ServiceLocator]	ClientDatabaseService: ✓ Registered
[2025-08-09 14:24:26.375]  	[INFO]		[ServiceLocator]	DatabasePerformanceMonitoringService: ✓ Registered
[2025-08-09 14:24:26.381]  	[INFO]		[ServiceLocator]	EnhancedDatabaseService: ✓ Registered
[2025-08-09 14:24:26.388]  	[INFO]		[ServiceLocator]	DatabaseSchemaValidator: ✓ Registered
[2025-08-09 14:24:26.402]  	[INFO]		[ServiceLocator]	ActivityTypeBaseService: ✓ Registered
[2025-08-09 14:24:26.421]  	[INFO]		[ServiceLocator]	ClientValidationService: ✓ Registered
[2025-08-09 14:24:26.428]  	[INFO]		[ServiceLocator]	ValidationService: ✓ Registered
[2025-08-09 14:24:26.434]  	[INFO]		[ServiceLocator]	IToastService: ✓ Registered
[2025-08-09 14:24:26.441]  	[INFO]		[ServiceLocator]	IWindowChromeService: ✓ Registered
[2025-08-09 14:24:26.458]  	[INFO]		[ServiceLocator]	Total registered services: 32
[2025-08-09 14:24:26.467]  	[INFO]		[ServiceLocator]	=== End Service Registration Status ===
[2025-08-09 14:24:26.474]  	[DEBUG]		[ServiceLocator]	Database services initialized successfully
[2025-08-09 14:24:26.483]  	[DEBUG]		[App]	UFU2 Application startup completed in 0ms
[2025-08-09 14:24:26.495]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 14:24:26.672]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: False
[2025-08-09 14:24:26.686]  	[DEBUG]		[TitleBarBehavior]	Title bar drag enabled for Grid
[2025-08-09 14:24:26.718]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: True
[2025-08-09 14:24:26.734]  	[DEBUG]		[TitleBarBehavior]	Title bar double-click maximize enabled for Grid
[2025-08-09 14:24:26.767]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 14:24:26.817]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:24:26.828]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 14:24:26.835]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:24:26.846]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 14:24:26.852]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:24:26.945]  	[DEBUG]		[MainWindow]	MainWindow loaded, initializing title bar behaviors
[2025-08-09 14:24:26.951]  	[DEBUG]		[MainWindow]	Title bar behavior target window set successfully
[2025-08-09 14:24:27.028]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:24:27.614]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل بنجاح
[2025-08-09 14:24:27.639]  	[DEBUG]		[CustomWindowChromeViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: WindowTitle(2), WindowIcon(1)
[2025-08-09 14:24:27.651]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 3, Time since interaction: 622.3139ms
[2025-08-09 14:24:27.660]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:24:27.667]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:24:27.674]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:24:27.680]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:24:27.686]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:24:27.693]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:24:27.699]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:24:29.746]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 3, Time since interaction: 2718.488ms
[2025-08-09 14:24:29.757]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:24:46.041]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:24:46.133]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 3, Time since interaction: 19104.9471ms
[2025-08-09 14:24:46.140]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:24:46.209]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:24:46.216]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:24:46.223]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:24:46.231]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:24:46.237]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:24:46.246]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:06.027]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 14:25:06.552]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 14:25:07.064]  	[WARN]		[UIResponsivenessMonitoringService]	UI performance degradation detected: 400.0% increase in response time
[2025-08-09 14:25:25.451]  	[DEBUG]		[MemoryPressureHandler]	Memory check - Process: 292.1MB, System: 30.0%, Pressure: Normal
[2025-08-09 14:25:28.722]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:25:28.774]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:28.782]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:28.788]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:28.797]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:28.803]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:28.811]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:28.823]  	[INFO]		[MainWindow]	User clicked AddUserButton
[2025-08-09 14:25:28.834]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientDialog (NewClientView, OnDemand)
[2025-08-09 14:25:28.841]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientView (NewClientView, Immediate)
[2025-08-09 14:25:28.863]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 14:25:28.872]  	[DEBUG]		[ResourceManager]	Registered resource: NPersonalViewModel_64657722_638903427288721204 (BaseViewModel) for NPersonalViewModel
[2025-08-09 14:25:28.878]  	[DEBUG]		[NPersonalViewModel]	BaseViewModel memory management initialized for NPersonalViewModel
[2025-08-09 14:25:28.885]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:25:28.893]  	[DEBUG]		[ResourceManager]	Registered resource: PersonalInformationViewModel_45048586_638903427288934515 (BaseViewModel) for PersonalInformationViewModel
[2025-08-09 14:25:28.900]  	[DEBUG]		[PersonalInformationViewModel]	BaseViewModel memory management initialized for PersonalInformationViewModel
[2025-08-09 14:25:28.907]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:25:28.913]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel initialized
[2025-08-09 14:25:28.921]  	[DEBUG]		[ResourceManager]	Registered resource: ContactInformationViewModel_2784095_638903427289218765 (BaseViewModel) for ContactInformationViewModel
[2025-08-09 14:25:28.929]  	[DEBUG]		[ContactInformationViewModel]	BaseViewModel memory management initialized for ContactInformationViewModel
[2025-08-09 14:25:28.937]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:25:28.944]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 14:25:28.953]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel initialized
[2025-08-09 14:25:28.984]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage control initialized with optimization
[2025-08-09 14:25:29.049]  	[INFO]		[NActivityTabView]	NActivityTabView initialized with optimization
[2025-08-09 14:25:29.116]  	[DEBUG]		[ResourceManager]	Registered resource: NewClientViewModel_2803303_638903427291167176 (BaseViewModel) for NewClientViewModel
[2025-08-09 14:25:29.123]  	[DEBUG]		[NewClientViewModel]	BaseViewModel memory management initialized for NewClientViewModel
[2025-08-09 14:25:29.129]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:25:29.135]  	[DEBUG]		[ResourceManager]	Registered resource: PersonalInformationViewModel_25229732_638903427291359158 (BaseViewModel) for PersonalInformationViewModel
[2025-08-09 14:25:29.142]  	[DEBUG]		[PersonalInformationViewModel]	BaseViewModel memory management initialized for PersonalInformationViewModel
[2025-08-09 14:25:29.148]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:25:29.155]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel initialized
[2025-08-09 14:25:29.161]  	[DEBUG]		[ResourceManager]	Registered resource: ContactInformationViewModel_25741004_638903427291615111 (BaseViewModel) for ContactInformationViewModel
[2025-08-09 14:25:29.168]  	[DEBUG]		[ContactInformationViewModel]	BaseViewModel memory management initialized for ContactInformationViewModel
[2025-08-09 14:25:29.176]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:25:29.183]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 14:25:29.189]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel initialized
[2025-08-09 14:25:29.196]  	[DEBUG]		[ResourceManager]	Registered resource: ActivityManagementViewModel_30342446_638903427291969813 (BaseViewModel) for ActivityManagementViewModel
[2025-08-09 14:25:29.204]  	[DEBUG]		[ActivityManagementViewModel]	BaseViewModel memory management initialized for ActivityManagementViewModel
[2025-08-09 14:25:29.210]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:25:29.219]  	[DEBUG]		[ActivityManagementViewModel]	Set default activity status for MainCommercial
[2025-08-09 14:25:29.226]  	[DEBUG]		[CpiLocationService]	Retrieved wilayas from cache
[2025-08-09 14:25:29.232]  	[INFO]		[ActivityManagementViewModel]	Loaded 58 CPI Wilayas
[2025-08-09 14:25:29.237]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel initialized
[2025-08-09 14:25:29.244]  	[DEBUG]		[ResourceManager]	Registered resource: NotesManagementViewModel_4646565_638903427292440781 (BaseViewModel) for NotesManagementViewModel
[2025-08-09 14:25:29.249]  	[DEBUG]		[NotesManagementViewModel]	BaseViewModel memory management initialized for NotesManagementViewModel
[2025-08-09 14:25:29.254]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:25:29.260]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 14:25:29.267]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel initialized
[2025-08-09 14:25:29.273]  	[DEBUG]		[NewClientViewModel]	Services initialized successfully
[2025-08-09 14:25:29.279]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 14:25:29.285]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel initialized with component ViewModels
[2025-08-09 14:25:29.294]  	[INFO]		[NewClientView]	Phone numbers synchronization setup completed (bidirectional sync disabled for tab independence)
[2025-08-09 14:25:29.301]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 14:25:29.307]  	[INFO]		[NewClientView]	NameFr real-time synchronization setup completed with optimization
[2025-08-09 14:25:29.313]  	[INFO]		[NewClientView]	Save data transfer mechanism setup completed
[2025-08-09 14:25:29.319]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: NewClientView (NewClientView)
[2025-08-09 14:25:29.328]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ActivityTypes (Priority: Low)
[2025-08-09 14:25:29.334]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ValidationRules (Priority: Low)
[2025-08-09 14:25:29.340]  	[DEBUG]		[NewClientView]	Queued background initialization tasks for NewClientView
[2025-08-09 14:25:29.348]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientView - 505ms (Success: True)
[2025-08-09 14:25:29.356]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientDialog - 524ms (Success: True)
[2025-08-09 14:25:29.362]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 2, misses: 1)
[2025-08-09 14:25:29.375]  	[DEBUG]		[NewClientView]	Background activity types preloading completed
[2025-08-09 14:25:29.381]  	[DEBUG]		[MainWindow]	Opening NewClientView dialog with optimized size: 900x560 (Height-based width calculation)
[2025-08-09 14:25:29.383]  	[INFO]		[ClientValidationService]	Client validation completed. Valid: False, Errors: 1
[2025-08-09 14:25:29.385]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ActivityTypes in 32ms
[2025-08-09 14:25:29.406]  	[DEBUG]		[NewClientView]	Background validation rules preloading completed
[2025-08-09 14:25:29.415]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ValidationRules in 63ms
[2025-08-09 14:25:29.432]  	[DEBUG]		[ProfileImageConverter]	Male default image loaded and cached
[2025-08-09 14:25:29.475]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 14:25:30.082]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage loaded - Gender: 0
[2025-08-09 14:25:30.094]  	[INFO]		[NActivityTabView]	NActivityTabView loaded successfully
[2025-08-09 14:25:30.235]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 3, Time since interaction: 1512.5535ms
[2025-08-09 14:25:30.253]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:25:30.309]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 1272.7872ms
[2025-08-09 14:25:30.323]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:30.336]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1442.6008ms
[2025-08-09 14:25:30.349]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:30.378]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1456.7449ms
[2025-08-09 14:25:30.388]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:30.409]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1292.491ms
[2025-08-09 14:25:30.415]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:30.458]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1320.9714ms
[2025-08-09 14:25:30.468]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:30.479]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1317.5679ms
[2025-08-09 14:25:30.485]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:30.502]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1305.5072ms
[2025-08-09 14:25:30.510]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:30.516]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1272.468ms
[2025-08-09 14:25:30.523]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:30.550]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:30.556]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:30.569]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:30.579]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:30.599]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:30.606]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:30.844]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:30.851]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:30.858]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:30.864]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:30.871]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:30.878]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:30.969]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:30.978]  	[DEBUG]		[NPersonalViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:30.985]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:30.993]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:31.017]  	[DEBUG]		[NewClientViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:31.031]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:31.038]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:31.044]  	[DEBUG]		[ActivityManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:31.052]  	[DEBUG]		[NotesManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:31.066]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:31.072]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:31.077]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:31.084]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:31.089]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:31.095]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:31.272]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 3, Time since interaction: 2549.4175ms
[2025-08-09 14:25:31.278]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 14:25:31.383]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 1, Time since interaction: 2346.6898ms
[2025-08-09 14:25:31.389]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 14:25:31.395]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 0, Time since interaction: 2502.1297ms
[2025-08-09 14:25:31.401]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 14:25:31.437]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 0, Time since interaction: 2515.2783ms
[2025-08-09 14:25:31.449]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 14:25:31.456]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 0, Time since interaction: 2339.8707ms
[2025-08-09 14:25:31.464]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 14:25:31.490]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 0, Time since interaction: 2354.5647ms
[2025-08-09 14:25:31.496]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 14:25:31.538]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 0, Time since interaction: 2377.3016ms
[2025-08-09 14:25:31.547]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 14:25:31.554]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 0, Time since interaction: 2357.3194ms
[2025-08-09 14:25:31.561]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 14:25:31.607]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Active to Background - Focus: False, Notifications/sec: 0, Time since interaction: 2363.6198ms
[2025-08-09 14:25:31.613]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Responsive to Conservative based on UI state: Background
[2025-08-09 14:25:33.904]  	[DEBUG]		[NPersonalViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: PhoneNumbersCollection(1)
[2025-08-09 14:25:35.265]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:25:35.272]  	[DEBUG]		[NPersonalViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:25:35.282]  	[DEBUG]		[PersonalInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:25:35.290]  	[DEBUG]		[ContactInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:25:35.300]  	[DEBUG]		[NewClientViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:25:35.307]  	[DEBUG]		[PersonalInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:25:35.315]  	[DEBUG]		[ContactInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:25:35.323]  	[DEBUG]		[ActivityManagementViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:25:35.331]  	[DEBUG]		[NotesManagementViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:25:35.382]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 3, Time since interaction: 117.6086ms
[2025-08-09 14:25:35.393]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:25:35.412]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:35.422]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:35.430]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:35.437]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:35.443]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:35.450]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:35.461]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 189.1174ms
[2025-08-09 14:25:35.468]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:25:35.475]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 192.7018ms
[2025-08-09 14:25:35.483]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:25:35.540]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 250.1468ms
[2025-08-09 14:25:35.550]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:25:35.567]  	[DEBUG]		[NewClientViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 267.4087ms
[2025-08-09 14:25:35.574]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:25:35.583]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 276.2146ms
[2025-08-09 14:25:35.590]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:25:35.836]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 14:25:35.851]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 535.2997ms
[2025-08-09 14:25:35.860]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:25:35.869]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 14:25:35.882]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 558.6192ms
[2025-08-09 14:25:35.893]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:25:35.975]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 0, Time since interaction: 644.1995ms
[2025-08-09 14:25:35.983]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:25:36.024]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:36.032]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:36.041]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:36.051]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:36.063]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:36.077]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:36.196]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-09 14:25:36.207]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-09 14:25:36.215]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-09 14:25:36.221]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-09 14:25:36.228]  	[DEBUG]		[NewClientView]	NameFr synced to main ViewModel: 'DRIDI YACINE'
[2025-08-09 14:25:36.235]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-09 14:25:36.241]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-09 14:25:36.248]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-09 14:25:36.254]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-09 14:25:36.262]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 14:25:36.289]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 14:25:36.620]  	[DEBUG]		[NPersonalView]	Checking for duplicate clients with NameFr: 'DRIDI YACINE'
[2025-08-09 14:25:36.643]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:25:36.820]  	[INFO]		[DuplicateClientDetectionService]	Found 1 duplicate clients for NameFr: DRIDI YACINE
[2025-08-09 14:25:36.828]  	[INFO]		[NPersonalView]	Found 1 duplicate clients for 'DRIDI YACINE'
[2025-08-09 14:25:36.850]  	[DEBUG]		[ResourceManager]	Registered resource: DuplicateClientDetectionDialogViewModel_8574195_638903427368499887 (BaseViewModel) for DuplicateClientDetectionDialogViewModel
[2025-08-09 14:25:36.856]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	BaseViewModel memory management initialized for DuplicateClientDetectionDialogViewModel
[2025-08-09 14:25:36.862]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:25:36.870]  	[DEBUG]		[DuplicateClientDetectionDialog]	DuplicateClientDetectionDialog ViewModel initialized with 1 duplicate clients
[2025-08-09 14:25:36.876]  	[INFO]		[DuplicateClientDetectionDialog]	Created DuplicateClientDetectionDialog for 'DRIDI YACINE' with 1 duplicates
[2025-08-09 14:25:37.093]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Processed 4 Normal priority property notifications
[2025-08-09 14:25:37.131]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:37.140]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:37.151]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:37.158]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:37.165]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:37.172]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:37.212]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:37.268]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:37.331]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:37.377]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:37.400]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:37.408]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:37.427]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 3, Time since interaction: 2162.6307ms
[2025-08-09 14:25:37.436]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:37.473]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:37.482]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:37.490]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:37.498]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:37.510]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:37.517]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:37.552]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2261.7197ms
[2025-08-09 14:25:37.561]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:37.956]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 4, Time since interaction: 1085.9ms
[2025-08-09 14:25:37.964]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:38.390]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 2, Time since interaction: 2128.0079ms
[2025-08-09 14:25:38.398]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:38.786]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2589.9797ms
[2025-08-09 14:25:38.793]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:38.890]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 3574.2657ms
[2025-08-09 14:25:38.897]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:39.013]  	[DEBUG]		[NPersonalViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(1)
[2025-08-09 14:25:39.020]  	[DEBUG]		[PersonalInformationViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(1)
[2025-08-09 14:25:39.107]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 3783.2835ms
[2025-08-09 14:25:39.114]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:39.124]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 3793.0685ms
[2025-08-09 14:25:39.130]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:39.502]  	[DEBUG]		[NewClientViewModel]	PropertyChanged Performance - Total: 2, Batched: 1, Immediate: 1, Efficiency: 50.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(1)
[2025-08-09 14:25:39.592]  	[DEBUG]		[PersonalInformationViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(1)
[2025-08-09 14:25:39.856]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 4020.4617ms
[2025-08-09 14:25:39.863]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:39.870]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 4072.7629ms
[2025-08-09 14:25:39.878]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:39.940]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 4, Time since interaction: 3069.8564ms
[2025-08-09 14:25:39.947]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:42.007]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	PropertyChanged Performance - Total: 4, Batched: 4, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: ContentMessage(1), HeaderText(1), DuplicateClients(1), SelectedClient(1)
[2025-08-09 14:25:42.164]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:42.173]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:42.180]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:42.187]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:42.194]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:42.200]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:42.248]  	[INFO]		[DuplicateClientDetectionDialogViewModel]	Using selected client: D01
[2025-08-09 14:25:42.260]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:25:42.273]  	[INFO]		[DuplicateClientDetectionService]	Retrieved complete client data for: D01
[2025-08-09 14:25:42.280]  	[DEBUG]		[DuplicateClientDetectionDialog]	Dialog close requested with result: True
[2025-08-09 14:25:42.295]  	[INFO]		[NPersonalView]	User selected existing client: D01
[2025-08-09 14:25:42.303]  	[INFO]		[NPersonalView]	Loading client data for: D01
[2025-08-09 14:25:42.320]  	[INFO]		[NPersonalView]	Loaded 4 phone numbers into collection
[2025-08-09 14:25:42.327]  	[DEBUG]		[NPersonalView]	Found NewClientViewModel in parent: StackPanel
[2025-08-09 14:25:42.337]  	[INFO]		[PersonalInformationViewModel]	Personal information loaded for client: D01
[2025-08-09 14:25:42.343]  	[INFO]		[NewClientViewModel]	Set editing state for existing client: D01
[2025-08-09 14:25:42.350]  	[INFO]		[NPersonalView]	Set editing state for existing client in NewClientViewModel: D01
[2025-08-09 14:25:42.356]  	[INFO]		[NPersonalView]	Successfully loaded client data for: D01
[2025-08-09 14:25:42.362]  	[INFO]		[NPersonalView]	Displaying user success toast: تم التحميل - تم تحميل بيانات العميل بنجاح
معرف العميل: D01
[2025-08-09 14:25:42.368]  	[DEBUG]		[ToastService]	Displaying Success toast: تم التحميل - تم تحميل بيانات العميل بنجاح
معرف العميل: D01
[2025-08-09 14:25:42.382]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل
[2025-08-09 14:25:42.397]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 14:25:42.404]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل
[2025-08-09 14:25:42.412]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 14:25:42.426]  	[INFO]		[ContactInformationViewModel]	Phone number added: 0656-32-77-19
[2025-08-09 14:25:42.434]  	[DEBUG]		[NPersonalViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 14:25:42.441]  	[DEBUG]		[NewClientViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 14:25:42.447]  	[DEBUG]		[PersonalInformationViewModel]	Processed 6 Normal priority property notifications
[2025-08-09 14:25:42.574]  	[DEBUG]		[ProfileImageConverter]	Female default image loaded and cached
[2025-08-09 14:25:42.591]  	[DEBUG]		[PersonalInformationViewModel]	Processed 6 Normal priority property notifications
[2025-08-09 14:25:42.612]  	[DEBUG]		[ContactInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 14:25:42.622]  	[DEBUG]		[NewClientViewModel]	Processed 6 Normal priority property notifications
[2025-08-09 14:25:42.824]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:42.880]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:42.904]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:42.923]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:42.935]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:42.948]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:42.977]  	[DEBUG]		[ClientProfileImage]	Gender changed to: 1 (Female)
[2025-08-09 14:25:43.104]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 9, Time since interaction: 785.1863ms
[2025-08-09 14:25:43.120]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:43.162]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 6, Time since interaction: 739.7686ms
[2025-08-09 14:25:43.181]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:43.551]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 1125.2613ms
[2025-08-09 14:25:43.568]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:43.623]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 3, Time since interaction: 1330.6239ms
[2025-08-09 14:25:43.631]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:43.649]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 8, Time since interaction: 1201.948ms
[2025-08-09 14:25:43.656]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:43.694]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 6, Time since interaction: 1356.4496ms
[2025-08-09 14:25:43.700]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:44.027]  	[DEBUG]		[NPersonalViewModel]	PropertyChanged Performance - Total: 9, Batched: 9, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: PhoneNumber(3), NameAr(1), BirthDate(1), BirthPlace(1), Gender(1)
[2025-08-09 14:25:44.058]  	[DEBUG]		[PersonalInformationViewModel]	PropertyChanged Performance - Total: 6, Batched: 6, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameAr(1), BirthDate(1), BirthPlace(1), Gender(1), Address(1)
[2025-08-09 14:25:44.105]  	[DEBUG]		[ContactInformationViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: PrimaryPhoneNumber(1)
[2025-08-09 14:25:44.262]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:44.269]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:44.275]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:44.281]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:44.287]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:44.294]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:44.353]  	[INFO]		[NPersonalView]	Opening phone numbers dialog
[2025-08-09 14:25:44.361]  	[INFO]		[NPersonalView]	Valid phone number synced to collection before opening dialog
[2025-08-09 14:25:44.394]  	[INFO]		[SaveCancelButtonsControl]	SaveCancelButtonsControl initialized
[2025-08-09 14:25:44.401]  	[DEBUG]		[ResourceManager]	Registered resource: PhoneNumbersDialogViewModel_14847872_638903427444015896 (BaseViewModel) for PhoneNumbersDialogViewModel
[2025-08-09 14:25:44.407]  	[DEBUG]		[PhoneNumbersDialogViewModel]	BaseViewModel memory management initialized for PhoneNumbersDialogViewModel
[2025-08-09 14:25:44.412]  	[DEBUG]		[PhoneNumbersDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:25:44.418]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 14:25:44.636]  	[DEBUG]		[PhoneNumbersDialogViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 14:25:44.654]  	[DEBUG]		[NewClientViewModel]	PropertyChanged Performance - Total: 8, Batched: 8, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: IsEditingExistingClient(1), ExistingClientUid(1), NameAr(1), BirthDate(1), BirthPlace(1)
[2025-08-09 14:25:44.699]  	[DEBUG]		[PersonalInformationViewModel]	PropertyChanged Performance - Total: 6, Batched: 6, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameAr(1), BirthDate(1), BirthPlace(1), Gender(1), Address(1)
[2025-08-09 14:25:44.708]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2282.1012ms
[2025-08-09 14:25:44.716]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:44.746]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 3, Time since interaction: 2454.3655ms
[2025-08-09 14:25:44.753]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:44.811]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 8, Time since interaction: 2363.6495ms
[2025-08-09 14:25:44.821]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:45.041]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 6, Time since interaction: 2703.3087ms
[2025-08-09 14:25:45.056]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:45.227]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 9, Time since interaction: 2908.3015ms
[2025-08-09 14:25:45.234]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:45.245]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 6, Time since interaction: 2823.1428ms
[2025-08-09 14:25:45.252]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:45.264]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:45.271]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:45.282]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:45.289]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:45.296]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:45.302]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:45.383]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل
[2025-08-09 14:25:45.412]  	[DEBUG]		[PhoneNumbersDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 2, Time since interaction: 982.1101ms
[2025-08-09 14:25:45.427]  	[DEBUG]		[PhoneNumbersDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:45.539]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:45.549]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:45.555]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:45.563]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:45.569]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:45.580]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:46.472]  	[DEBUG]		[PhoneNumbersDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 2, Time since interaction: 2042.9504ms
[2025-08-09 14:25:46.479]  	[DEBUG]		[PhoneNumbersDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:46.489]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:46.502]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:46.513]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:46.519]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:46.525]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:46.531]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:46.548]  	[INFO]		[SaveCancelButtonsControl]	Cancel button clicked in SaveCancelButtonsControl
[2025-08-09 14:25:46.557]  	[INFO]		[NPersonalView]	Phone numbers dialog cancelled - no changes made
[2025-08-09 14:25:46.627]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:46.634]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:46.645]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:46.653]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:46.663]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:46.670]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:47.171]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	PropertyChanged Performance - Total: 3, Batched: 1, Immediate: 2, Efficiency: 33.3%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: SelectedClient(1)
[2025-08-09 14:25:49.439]  	[DEBUG]		[PhoneNumbersDialogViewModel]	PropertyChanged Performance - Total: 2, Batched: 2, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: PhoneNumbers(1), PhoneTypes(1)
[2025-08-09 14:25:51.551]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:51.558]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:51.565]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:51.571]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:51.579]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:51.585]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:51.676]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:51.683]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:51.689]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:51.695]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:51.701]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:51.708]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:52.358]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:52.374]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:52.381]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:52.389]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:52.396]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:52.404]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:52.414]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 14:25:52.459]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 14:25:52.520]  	[DEBUG]		[ClientProfileImage]	Gender changed to: 0 (Male)
[2025-08-09 14:25:52.773]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 358.9009ms
[2025-08-09 14:25:52.783]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:52.808]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 468.7115ms
[2025-08-09 14:25:52.817]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:54.122]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:54.128]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:54.134]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:54.141]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:54.146]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:54.152]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:54.209]  	[DEBUG]		[NPersonalViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: Gender(1)
[2025-08-09 14:25:54.217]  	[DEBUG]		[PersonalInformationViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: Gender(1)
[2025-08-09 14:25:54.232]  	[INFO]		[NewClientView]	Transferred PersonalInfo data - NameFr: 'DRIDI YACINE', NameAr: 'DRIDI YACINE', BirthDate: '20/08/1987', Gender: 0
[2025-08-09 14:25:54.239]  	[INFO]		[NewClientView]	Transferred 4 phone numbers to ViewModel for saving
[2025-08-09 14:25:54.245]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-09 14:25:54.254]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-09 14:25:54.263]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-09 14:25:54.270]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-09 14:25:54.281]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:25:54.299]  	[INFO]		[ClientDatabaseService]	Client updated successfully: D01
[2025-08-09 14:25:54.305]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:25:54.311]  	[INFO]		[NewClientViewModel]	Client saved successfully: D01
[2025-08-09 14:25:54.318]  	[INFO]		[ErrorManager]	Displaying user success toast: نجح الحفظ - تم تحديث بيانات العميل بنجاح
[2025-08-09 14:25:54.325]  	[DEBUG]		[ToastService]	Displaying Success toast: نجح الحفظ - تم تحديث بيانات العميل بنجاح
[2025-08-09 14:25:54.328]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:25:54.339]  	[DEBUG]		[ArchiveDatabaseService]	Logged data update: Client[D01].Gender changed from 1 to 0
[2025-08-09 14:25:54.342]  	[INFO]		[ToastNotification]	Toast notification created: Success - نجح الحفظ
[2025-08-09 14:25:54.359]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 14:25:54.367]  	[INFO]		[ToastService]	Desktop toast displayed: Success - نجح الحفظ
[2025-08-09 14:25:54.376]  	[DEBUG]		[PersonalInformationViewModel]	Personal information cleared
[2025-08-09 14:25:54.384]  	[DEBUG]		[ContactInformationViewModel]	Contact information cleared
[2025-08-09 14:25:54.392]  	[DEBUG]		[ActivityManagementViewModel]	Set default activity status for MainCommercial
[2025-08-09 14:25:54.398]  	[DEBUG]		[ActivityManagementViewModel]	Activity management data cleared
[2025-08-09 14:25:54.408]  	[DEBUG]		[NotesManagementViewModel]	Notes cleared
[2025-08-09 14:25:54.414]  	[DEBUG]		[NewClientViewModel]	Cleared editing state - returned to new client creation mode
[2025-08-09 14:25:54.420]  	[INFO]		[NewClientViewModel]	Dialog closed and data cleared
[2025-08-09 14:25:54.427]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 14:25:54.452]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 14:25:54.458]  	[DEBUG]		[PersonalInformationViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 14:25:54.466]  	[DEBUG]		[ContactInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 14:25:54.479]  	[DEBUG]		[ActivityManagementViewModel]	Processed 6 Normal priority property notifications
[2025-08-09 14:25:54.486]  	[DEBUG]		[NotesManagementViewModel]	Processed 3 Normal priority property notifications
[2025-08-09 14:25:54.494]  	[DEBUG]		[NewClientViewModel]	Processed 12 Normal priority property notifications
[2025-08-09 14:25:54.544]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:54.550]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:54.556]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:54.562]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:54.568]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:54.574]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:54.679]  	[DEBUG]		[NewClientViewModel]	PropertyChanged Performance - Total: 15, Batched: 12, Immediate: 3, Efficiency: 80.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: IsEditingExistingClient(1), ExistingClientUid(1), Gender(1), NameFr(1), NameAr(1)
[2025-08-09 14:25:54.727]  	[DEBUG]		[PersonalInformationViewModel]	PropertyChanged Performance - Total: 7, Batched: 7, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: Gender(1), NameFr(1), NameAr(1), BirthDate(1), BirthPlace(1)
[2025-08-09 14:25:54.881]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 7, Time since interaction: 505.2325ms
[2025-08-09 14:25:54.888]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:54.898]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 2, Time since interaction: 514.4588ms
[2025-08-09 14:25:54.913]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:54.926]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 6, Time since interaction: 527.5135ms
[2025-08-09 14:25:54.936]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:54.944]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 6, Time since interaction: 536.7081ms
[2025-08-09 14:25:54.953]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:54.962]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2547.4042ms
[2025-08-09 14:25:54.974]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:54.982]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2642.616ms
[2025-08-09 14:25:54.988]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:55.084]  	[DEBUG]		[ContactInformationViewModel]	PropertyChanged Performance - Total: 2, Batched: 2, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: PrimaryPhoneNumber(2)
[2025-08-09 14:25:55.093]  	[DEBUG]		[ActivityManagementViewModel]	PropertyChanged Performance - Total: 6, Batched: 6, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: CurrentActivity(1), CurrentFileCheckStates(1), G12SelectedYears(1), BISSelectedYears(1), G12DisplayText(1)
[2025-08-09 14:25:55.100]  	[DEBUG]		[NotesManagementViewModel]	PropertyChanged Performance - Total: 6, Batched: 6, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NotesDisplayText(2), NotesCount(2), HasNotes(2)
[2025-08-09 14:25:55.224]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 15, Time since interaction: 738.9935ms
[2025-08-09 14:25:55.230]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:25:56.899]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 7, Time since interaction: 2523.5599ms
[2025-08-09 14:25:56.906]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:56.931]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 2, Time since interaction: 2546.9697ms
[2025-08-09 14:25:56.937]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:56.947]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 6, Time since interaction: 2548.9178ms
[2025-08-09 14:25:56.954]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:56.961]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 6, Time since interaction: 2553.4535ms
[2025-08-09 14:25:56.968]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:57.239]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 15, Time since interaction: 2753.5876ms
[2025-08-09 14:25:57.246]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:25:57.332]  	[INFO]		[ToastNotification]	Toast notification closed: Success - نجح الحفظ
[2025-08-09 14:25:57.368]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:57.387]  	[DEBUG]		[NPersonalViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:57.418]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:57.442]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:57.449]  	[DEBUG]		[NewClientViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:57.456]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:57.494]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:57.502]  	[DEBUG]		[ActivityManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:57.510]  	[DEBUG]		[NotesManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:57.517]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:57.526]  	[DEBUG]		[PhoneNumbersDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:25:57.550]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:57.556]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:57.563]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:57.570]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:25:57.577]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:25:57.583]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:25:57.972]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 7, Time since interaction: 3595.8627ms
[2025-08-09 14:25:57.978]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:25:57.984]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 2, Time since interaction: 3600.3097ms
[2025-08-09 14:25:57.994]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:25:58.001]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 6, Time since interaction: 3603.0104ms
[2025-08-09 14:25:58.010]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:25:58.017]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 6, Time since interaction: 3609.2618ms
[2025-08-09 14:25:58.024]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:25:58.031]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 3, Time since interaction: 22765.7991ms
[2025-08-09 14:25:58.036]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:25:58.043]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 1, Time since interaction: 5629.1769ms
[2025-08-09 14:25:58.052]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:25:58.373]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 1, Time since interaction: 6033.6883ms
[2025-08-09 14:25:58.380]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:25:58.455]  	[DEBUG]		[PhoneNumbersDialogViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 2, Time since interaction: 14025.2113ms
[2025-08-09 14:25:58.467]  	[DEBUG]		[PhoneNumbersDialogViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:25:58.480]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 1, Time since interaction: 16054.174ms
[2025-08-09 14:25:58.515]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:25:58.573]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 3, Time since interaction: 16280.9831ms
[2025-08-09 14:25:58.579]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:25:58.806]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 15, Time since interaction: 4320.5103ms
[2025-08-09 14:25:58.813]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:08.336]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:08.346]  	[DEBUG]		[NPersonalViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:08.354]  	[DEBUG]		[PersonalInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:08.362]  	[DEBUG]		[ContactInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:08.370]  	[DEBUG]		[NewClientViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:08.382]  	[DEBUG]		[PersonalInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:08.390]  	[DEBUG]		[ContactInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:08.417]  	[DEBUG]		[ActivityManagementViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:08.445]  	[DEBUG]		[NotesManagementViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:08.455]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:08.473]  	[DEBUG]		[PhoneNumbersDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:08.488]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 7, Time since interaction: 106.1889ms
[2025-08-09 14:26:08.499]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:08.545]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 2, Time since interaction: 154.7397ms
[2025-08-09 14:26:08.551]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:08.558]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 6, Time since interaction: 140.9476ms
[2025-08-09 14:26:08.564]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:08.630]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 6, Time since interaction: 184.6695ms
[2025-08-09 14:26:08.638]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:08.659]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 3, Time since interaction: 323.2724ms
[2025-08-09 14:26:08.666]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:08.722]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 375.2395ms
[2025-08-09 14:26:08.728]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:08.871]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:08.878]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:08.885]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:08.892]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:08.898]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:08.904]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:09.060]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 706.7444ms
[2025-08-09 14:26:09.078]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:09.303]  	[DEBUG]		[PhoneNumbersDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 2, Time since interaction: 829.8725ms
[2025-08-09 14:26:09.309]  	[DEBUG]		[PhoneNumbersDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:09.315]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 953.0444ms
[2025-08-09 14:26:09.322]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:09.389]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 3, Time since interaction: 933.5608ms
[2025-08-09 14:26:09.396]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:09.622]  	[DEBUG]		[NewClientViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 15, Time since interaction: 1251.878ms
[2025-08-09 14:26:09.629]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:09.739]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:09.745]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:09.751]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:09.758]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:09.765]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:09.771]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:09.854]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:09.861]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:09.867]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:09.875]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:09.881]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:09.888]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:10.255]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:10.262]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:10.268]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:10.275]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:10.281]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:10.289]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:10.454]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2091.3682ms
[2025-08-09 14:26:10.461]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:10.468]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 3, Time since interaction: 2012.389ms
[2025-08-09 14:26:10.475]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:10.688]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 15, Time since interaction: 2317.5783ms
[2025-08-09 14:26:10.694]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:10.786]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 7, Time since interaction: 2404.2594ms
[2025-08-09 14:26:10.794]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:10.918]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 2, Time since interaction: 2528.4118ms
[2025-08-09 14:26:10.928]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:10.970]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 6, Time since interaction: 2552.2182ms
[2025-08-09 14:26:10.977]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:11.142]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 6, Time since interaction: 2696.7773ms
[2025-08-09 14:26:11.148]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:11.155]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 3, Time since interaction: 2819.0741ms
[2025-08-09 14:26:11.162]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:11.168]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2821.7495ms
[2025-08-09 14:26:11.174]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:11.250]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2896.0104ms
[2025-08-09 14:26:11.257]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:11.405]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:11.411]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:11.418]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:11.426]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:11.434]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:11.444]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:11.626]  	[DEBUG]		[PhoneNumbersDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 2, Time since interaction: 3152.6656ms
[2025-08-09 14:26:11.633]  	[DEBUG]		[PhoneNumbersDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:11.889]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:11.896]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:11.902]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:11.908]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:11.915]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:11.921]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:12.168]  	[DEBUG]		[NPersonalView]	Checking for duplicate clients with NameFr: 'DRIDI YACINE'
[2025-08-09 14:26:12.175]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:26:12.181]  	[INFO]		[DuplicateClientDetectionService]	Found 1 duplicate clients for NameFr: DRIDI YACINE
[2025-08-09 14:26:12.191]  	[INFO]		[NPersonalView]	Found 1 duplicate clients for 'DRIDI YACINE'
[2025-08-09 14:26:12.207]  	[DEBUG]		[ResourceManager]	Registered resource: DuplicateClientDetectionDialogViewModel_43253509_638903427722077553 (BaseViewModel) for DuplicateClientDetectionDialogViewModel
[2025-08-09 14:26:12.214]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	BaseViewModel memory management initialized for DuplicateClientDetectionDialogViewModel
[2025-08-09 14:26:12.220]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:26:12.226]  	[DEBUG]		[DuplicateClientDetectionDialog]	DuplicateClientDetectionDialog ViewModel initialized with 1 duplicate clients
[2025-08-09 14:26:12.232]  	[INFO]		[DuplicateClientDetectionDialog]	Created DuplicateClientDetectionDialog for 'DRIDI YACINE' with 1 duplicates
[2025-08-09 14:26:12.419]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Processed 4 Normal priority property notifications
[2025-08-09 14:26:12.469]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:12.476]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:12.483]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:12.492]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:12.504]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:12.510]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:12.548]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:12.557]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:12.564]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:12.571]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:12.578]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:12.584]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:12.645]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:12.652]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:12.661]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:12.666]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:12.673]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:12.679]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:13.238]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 4, Time since interaction: 1011.1979ms
[2025-08-09 14:26:13.244]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:26:13.525]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:13.532]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:13.538]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:13.545]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:13.551]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:13.560]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:13.586]  	[INFO]		[DuplicateClientDetectionDialogViewModel]	User chose to create new client
[2025-08-09 14:26:13.594]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Smart batching triggered immediate flush - Changes: 1, UI State: Active, Strategy: Responsive
[2025-08-09 14:26:13.602]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Processed 1 High priority property notifications
[2025-08-09 14:26:13.608]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	All batched notifications flushed
[2025-08-09 14:26:13.615]  	[DEBUG]		[DuplicateClientDetectionDialog]	Dialog close requested with result: False
[2025-08-09 14:26:13.622]  	[INFO]		[NPersonalView]	User chose to create new client
[2025-08-09 14:26:13.770]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:13.784]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:13.792]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:13.799]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:13.810]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:13.821]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:16.198]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:16.208]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:16.214]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:16.219]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:16.225]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:16.231]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:16.276]  	[DEBUG]		[PersonalInformationViewModel]	Personal information cleared
[2025-08-09 14:26:16.283]  	[DEBUG]		[ContactInformationViewModel]	Contact information cleared
[2025-08-09 14:26:16.289]  	[DEBUG]		[ActivityManagementViewModel]	Set default activity status for MainCommercial
[2025-08-09 14:26:16.296]  	[DEBUG]		[ActivityManagementViewModel]	Activity management data cleared
[2025-08-09 14:26:16.303]  	[DEBUG]		[NotesManagementViewModel]	Notes cleared
[2025-08-09 14:26:16.311]  	[DEBUG]		[NewClientViewModel]	Cleared editing state - returned to new client creation mode
[2025-08-09 14:26:16.317]  	[INFO]		[NewClientViewModel]	Dialog closed and data cleared
[2025-08-09 14:26:16.337]  	[DEBUG]		[ActivityManagementViewModel]	Processed 6 Normal priority property notifications
[2025-08-09 14:26:16.352]  	[DEBUG]		[NotesManagementViewModel]	Processed 3 Normal priority property notifications
[2025-08-09 14:26:16.359]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:16.365]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:16.371]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:16.378]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:16.384]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:16.391]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:16.402]  	[DEBUG]		[NewClientViewModel]	Processed 3 Normal priority property notifications
[2025-08-09 14:26:16.493]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 6, Time since interaction: 197.0067ms
[2025-08-09 14:26:16.499]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:26:16.524]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2930.3821ms
[2025-08-09 14:26:16.532]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:16.554]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 6, Time since interaction: 251.5645ms
[2025-08-09 14:26:16.564]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:26:17.195]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 3, Time since interaction: 842.6961ms
[2025-08-09 14:26:17.202]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:26:17.224]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	PropertyChanged Performance - Total: 5, Batched: 5, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: SelectedClient(2), ContentMessage(1), HeaderText(1), DuplicateClients(1)
[2025-08-09 14:26:18.022]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:18.029]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:18.037]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:18.044]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:18.050]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:18.058]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:18.128]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:18.135]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:18.144]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:18.151]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:18.159]  	[DEBUG]		[CustomWindowChromeViewModel]	Executing CloseCommand
[2025-08-09 14:26:18.166]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:18.208]  	[DEBUG]		[CustomWindowChromeViewModel]	Attempting to close window: UFU Client Management (Type: MainWindow)
[2025-08-09 14:26:18.217]  	[DEBUG]		[WindowStateManager]	Attempting to close window: UFU Client Management (ForceClose: False)
[2025-08-09 14:26:18.225]  	[DEBUG]		[WindowStateManager]	Initiating normal window close
[2025-08-09 14:26:18.233]  	[INFO]		[MainWindow]	Application closing
[2025-08-09 14:26:18.241]  	[DEBUG]		[ResourceManager]	Registered resource: ConfirmationWindowViewModel_64678211_638903427782418534 (BaseViewModel) for ConfirmationWindowViewModel
[2025-08-09 14:26:18.249]  	[DEBUG]		[ConfirmationWindowViewModel]	BaseViewModel memory management initialized for ConfirmationWindowViewModel
[2025-08-09 14:26:18.258]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:26:18.266]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 14:26:18.273]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 14:26:18.283]  	[DEBUG]		[ConfirmationWindowViewModel]	Created exit confirmation dialog for UFU2
[2025-08-09 14:26:18.299]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow initialized
[2025-08-09 14:26:18.322]  	[INFO]		[ConfirmationWindow]	ViewModel set for ConfirmationWindow
[2025-08-09 14:26:18.332]  	[INFO]		[ConfirmationWindow]	Showing ConfirmationWindow as modal dialog
[2025-08-09 14:26:18.420]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow loaded successfully
[2025-08-09 14:26:18.446]  	[DEBUG]		[ConfirmationWindowViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 14:26:18.863]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 3, Time since interaction: 2510.368ms
[2025-08-09 14:26:18.870]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:18.940]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 6, Time since interaction: 2644.3169ms
[2025-08-09 14:26:18.947]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:18.975]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 6, Time since interaction: 2672.6829ms
[2025-08-09 14:26:18.981]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:18.992]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:18.998]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:19.004]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:19.013]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:19.019]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:19.024]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:19.259]  	[DEBUG]		[ConfirmationWindowViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 7, Time since interaction: 975.4288ms
[2025-08-09 14:26:19.268]  	[DEBUG]		[ConfirmationWindowViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:26:19.803]  	[DEBUG]		[NewClientViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: G12DisplayText(1), BISDisplayText(1), NotesDisplayText(1)
[2025-08-09 14:26:19.891]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:19.897]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:19.903]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:19.910]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:19.916]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:19.922]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:19.999]  	[DEBUG]		[ConfirmationWindowViewModel]	Secondary action executed: إغلاق
[2025-08-09 14:26:20.009]  	[INFO]		[ConfirmationWindow]	Close requested with result: False
[2025-08-09 14:26:20.017]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-09 14:26:20.035]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-09 14:26:20.042]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 14:26:20.049]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 14:26:20.055]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-09 14:26:20.061]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 14:26:20.069]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 14:26:20.077]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 14:26:20.083]  	[DEBUG]		[ResourceManager]	Unregistered resource: ConfirmationWindowViewModel_64678211_638903427782418534
[2025-08-09 14:26:20.090]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 14:26:20.096]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 14:26:20.102]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-09 14:26:20.108]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 14:26:20.115]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 14:26:20.120]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 14:26:20.126]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow closing
[2025-08-09 14:26:20.147]  	[INFO]		[MainWindow]	Application exit cancelled by user
[2025-08-09 14:26:20.153]  	[INFO]		[WindowStateManager]	Window close initiated successfully: UFU Client Management
[2025-08-09 14:26:20.160]  	[DEBUG]		[CustomWindowChromeViewModel]	CloseCommand executed successfully for window: UFU Client Management
[2025-08-09 14:26:20.190]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:20.196]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:20.202]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:20.211]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:20.217]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:20.223]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:20.405]  	[DEBUG]		[ActivityManagementViewModel]	PropertyChanged Performance - Total: 6, Batched: 6, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: CurrentActivity(1), CurrentFileCheckStates(1), G12SelectedYears(1), BISSelectedYears(1), G12DisplayText(1)
[2025-08-09 14:26:20.412]  	[DEBUG]		[NotesManagementViewModel]	PropertyChanged Performance - Total: 6, Batched: 6, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NotesDisplayText(2), NotesCount(2), HasNotes(2)
[2025-08-09 14:26:20.749]  	[INFO]		[ViewMemoryOptimizationService]	High memory usage detected: 400MB
[2025-08-09 14:26:20.758]  	[INFO]		[ViewMemoryOptimizationService]	Performed selective cleanup of 0 views
[2025-08-09 14:26:20.786]  	[WARN]		[ResourceManager]	Memory pressure detected: 401.3125MB. Triggering cleanup.
[2025-08-09 14:26:20.795]  	[INFO]		[ResourceManager]	Forced cleanup completed
[2025-08-09 14:26:20.834]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 401.37MB working set
[2025-08-09 14:26:22.619]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:22.634]  	[DEBUG]		[NPersonalViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:22.650]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:22.664]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:22.671]  	[DEBUG]		[NewClientViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:22.678]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:22.685]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:22.691]  	[DEBUG]		[ActivityManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:22.697]  	[DEBUG]		[NotesManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:22.703]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:22.709]  	[DEBUG]		[PhoneNumbersDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:22.716]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:22.750]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:22.768]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:22.778]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:22.787]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:22.798]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:22.804]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:23.139]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 3, Time since interaction: 6786.508ms
[2025-08-09 14:26:23.145]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:23.388]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 7, Time since interaction: 15006.6436ms
[2025-08-09 14:26:23.394]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:23.450]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 2, Time since interaction: 15060.4003ms
[2025-08-09 14:26:23.457]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:23.463]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 6, Time since interaction: 7166.6811ms
[2025-08-09 14:26:23.469]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:23.605]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 1, Time since interaction: 10011.323ms
[2025-08-09 14:26:23.615]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:23.634]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 6, Time since interaction: 7331.3197ms
[2025-08-09 14:26:23.642]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:23.839]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 3, Time since interaction: 15503.1096ms
[2025-08-09 14:26:23.846]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:24.027]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 1, Time since interaction: 15680.1717ms
[2025-08-09 14:26:24.033]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:24.040]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 1, Time since interaction: 15686.5068ms
[2025-08-09 14:26:24.046]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:24.118]  	[DEBUG]		[PhoneNumbersDialogViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 2, Time since interaction: 15645.0852ms
[2025-08-09 14:26:24.125]  	[DEBUG]		[PhoneNumbersDialogViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:24.132]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 1, Time since interaction: 15769.3939ms
[2025-08-09 14:26:24.138]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:24.228]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 3, Time since interaction: 15772.4949ms
[2025-08-09 14:26:24.251]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:25.413]  	[DEBUG]		[CacheMonitoringService]	Performing cache monitoring
[2025-08-09 14:26:25.451]  	[ERROR]		[ActivityTypeBaseService]	Error getting cache health: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 14:26:25.456]  	[DEBUG]		[MemoryPressureHandler]	Memory check - Process: 371.5MB, System: 30.0%, Pressure: Normal
[2025-08-09 14:26:25.470]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for CpiLocationService: Needs Attention
[2025-08-09 14:26:25.478]  	[INFO]		[CacheMonitoringService]	Cache event [LowHitRatio] for ValidationService: Hit ratio: 0.0%
[2025-08-09 14:26:25.484]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for ValidationService: Low hit ratio: 0.0%
[2025-08-09 14:26:25.491]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for ActivityTypeBaseService: Error: The given key 'SearchCacheItems' was not present in the dictionary.
[2025-08-09 14:26:25.497]  	[INFO]		[CacheMonitoringService]	Cache event [LowHitRatio] for CraftTypeBaseService: Hit ratio: 0.0%
[2025-08-09 14:26:25.504]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for CraftTypeBaseService: Poor Performance
[2025-08-09 14:26:25.511]  	[INFO]		[CacheMonitoringService]	Cache event [LowHitRatio] for FileCheckBusinessRuleService: Hit ratio: 0.0%
[2025-08-09 14:26:25.517]  	[INFO]		[CacheMonitoringService]	Cache event [UnhealthyCache] for FileCheckBusinessRuleService: Low hit ratio: 0.0%
[2025-08-09 14:26:25.525]  	[INFO]		[CacheMonitoringService]	Cache monitoring summary - Services: 5, Unhealthy: 5, Total memory: 0.0MB, Avg hit ratio: 16.7%
[2025-08-09 14:26:25.531]  	[INFO]		[CacheMonitoringService]	Cache event [LowHitRatio] for Global: Average hit ratio (16.7%) below threshold (40.0%)
[2025-08-09 14:26:34.635]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:34.652]  	[DEBUG]		[NPersonalViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:34.664]  	[DEBUG]		[PersonalInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:34.671]  	[DEBUG]		[ContactInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:34.680]  	[DEBUG]		[NewClientViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:34.687]  	[DEBUG]		[PersonalInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:34.695]  	[DEBUG]		[ContactInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:34.701]  	[DEBUG]		[ActivityManagementViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:34.709]  	[DEBUG]		[NotesManagementViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:34.715]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:34.721]  	[DEBUG]		[PhoneNumbersDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:34.727]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:34.742]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:34.749]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:34.761]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:34.767]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:34.778]  	[DEBUG]		[CustomWindowChromeViewModel]	Executing CloseCommand
[2025-08-09 14:26:34.796]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:34.819]  	[DEBUG]		[CustomWindowChromeViewModel]	Attempting to close window: UFU Client Management (Type: MainWindow)
[2025-08-09 14:26:34.833]  	[DEBUG]		[WindowStateManager]	Attempting to close window: UFU Client Management (ForceClose: False)
[2025-08-09 14:26:34.867]  	[DEBUG]		[WindowStateManager]	Initiating normal window close
[2025-08-09 14:26:34.879]  	[INFO]		[MainWindow]	Application closing
[2025-08-09 14:26:34.891]  	[DEBUG]		[ResourceManager]	Registered resource: ConfirmationWindowViewModel_43270676_638903427948917120 (BaseViewModel) for ConfirmationWindowViewModel
[2025-08-09 14:26:34.901]  	[DEBUG]		[ConfirmationWindowViewModel]	BaseViewModel memory management initialized for ConfirmationWindowViewModel
[2025-08-09 14:26:34.910]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:26:34.917]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 14:26:34.923]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel initialized
[2025-08-09 14:26:34.930]  	[DEBUG]		[ConfirmationWindowViewModel]	Created exit confirmation dialog for UFU2
[2025-08-09 14:26:34.941]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow initialized
[2025-08-09 14:26:34.947]  	[INFO]		[ConfirmationWindow]	ViewModel set for ConfirmationWindow
[2025-08-09 14:26:34.953]  	[INFO]		[ConfirmationWindow]	Showing ConfirmationWindow as modal dialog
[2025-08-09 14:26:35.012]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow loaded successfully
[2025-08-09 14:26:35.034]  	[DEBUG]		[ConfirmationWindowViewModel]	Processed 7 Normal priority property notifications
[2025-08-09 14:26:35.257]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 605.3874ms
[2025-08-09 14:26:35.266]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:35.277]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 612.8222ms
[2025-08-09 14:26:35.285]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:35.294]  	[DEBUG]		[PhoneNumbersDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 2, Time since interaction: 573.0598ms
[2025-08-09 14:26:35.301]  	[DEBUG]		[PhoneNumbersDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:35.310]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 638.6818ms
[2025-08-09 14:26:35.319]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:35.330]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 3, Time since interaction: 615.1263ms
[2025-08-09 14:26:35.338]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:35.349]  	[DEBUG]		[NewClientViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 3, Time since interaction: 669.2834ms
[2025-08-09 14:26:35.361]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:35.369]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 7, Time since interaction: 682.6467ms
[2025-08-09 14:26:35.380]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:35.389]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 2, Time since interaction: 694.0024ms
[2025-08-09 14:26:35.397]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:35.407]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 6, Time since interaction: 705.5101ms
[2025-08-09 14:26:35.414]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:35.421]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 1, Time since interaction: 693.5641ms
[2025-08-09 14:26:35.429]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:35.436]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 6, Time since interaction: 727.198ms
[2025-08-09 14:26:35.444]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:35.452]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:35.461]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:35.468]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:35.475]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:35.481]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:35.487]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:35.518]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Background to Active - Focus: True, Notifications/sec: 3, Time since interaction: 882.5095ms
[2025-08-09 14:26:35.526]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Conservative to Responsive based on UI state: Active
[2025-08-09 14:26:35.909]  	[DEBUG]		[ConfirmationWindowViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 7, Time since interaction: 978.6939ms
[2025-08-09 14:26:35.936]  	[DEBUG]		[ConfirmationWindowViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:26:36.756]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:36.763]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:36.770]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:36.777]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:36.783]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:36.790]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:36.868]  	[DEBUG]		[ConfirmationWindowViewModel]	Secondary action executed: إغلاق
[2025-08-09 14:26:36.875]  	[INFO]		[ConfirmationWindow]	Close requested with result: False
[2025-08-09 14:26:36.882]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-09 14:26:36.889]  	[DEBUG]		[ConfirmationWindowViewModel]	All batched notifications flushed
[2025-08-09 14:26:36.895]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 14:26:36.901]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 14:26:36.908]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-09 14:26:36.914]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 14:26:36.923]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 14:26:36.929]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 14:26:36.936]  	[DEBUG]		[ResourceManager]	Unregistered resource: ConfirmationWindowViewModel_43270676_638903427948917120
[2025-08-09 14:26:36.944]  	[INFO]		[ResourceManager]	Cleaned up all resources for owner type: ConfirmationWindowViewModel (1 resources, 0 event subscriptions)
[2025-08-09 14:26:36.951]  	[INFO]		[WeakEventManager]	Removed all event handlers for owner type: ConfirmationWindowViewModel (0 handlers)
[2025-08-09 14:26:36.958]  	[DEBUG]		[ConfirmationWindowViewModel]	Memory management cleanup completed
[2025-08-09 14:26:36.967]  	[DEBUG]		[ConfirmationWindowViewModel]	Enhanced BaseViewModel disposed with cleanup
[2025-08-09 14:26:36.974]  	[DEBUG]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 14:26:36.982]  	[INFO]		[ConfirmationWindowViewModel]	ConfirmationWindowViewModel disposed
[2025-08-09 14:26:36.988]  	[INFO]		[ConfirmationWindow]	ConfirmationWindow closing
[2025-08-09 14:26:37.012]  	[INFO]		[MainWindow]	Application exit cancelled by user
[2025-08-09 14:26:37.045]  	[INFO]		[WindowStateManager]	Window close initiated successfully: UFU Client Management
[2025-08-09 14:26:37.062]  	[DEBUG]		[CustomWindowChromeViewModel]	CloseCommand executed successfully for window: UFU Client Management
[2025-08-09 14:26:37.123]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:37.131]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:37.141]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:37.148]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:37.156]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:37.163]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:37.337]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2685.7233ms
[2025-08-09 14:26:37.344]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:37.350]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2686.3069ms
[2025-08-09 14:26:37.357]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:37.400]  	[DEBUG]		[PhoneNumbersDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 2, Time since interaction: 2679.7416ms
[2025-08-09 14:26:37.408]  	[DEBUG]		[PhoneNumbersDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:37.415]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2743.6577ms
[2025-08-09 14:26:37.422]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:37.555]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 3, Time since interaction: 2840.2579ms
[2025-08-09 14:26:37.563]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:37.587]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 3, Time since interaction: 2907.1436ms
[2025-08-09 14:26:37.594]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:37.601]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 7, Time since interaction: 2914.4493ms
[2025-08-09 14:26:37.609]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:37.753]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 2, Time since interaction: 3057.9268ms
[2025-08-09 14:26:37.763]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:37.770]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 6, Time since interaction: 3068.9606ms
[2025-08-09 14:26:37.778]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:37.821]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 3094.003ms
[2025-08-09 14:26:37.829]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:37.836]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 6, Time since interaction: 3127.8024ms
[2025-08-09 14:26:37.844]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:37.867]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 3, Time since interaction: 3231.3156ms
[2025-08-09 14:26:37.879]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:26:38.060]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:38.086]  	[DEBUG]		[NPersonalViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:38.094]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:38.102]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:38.109]  	[DEBUG]		[NewClientViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:38.120]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:38.128]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:38.137]  	[DEBUG]		[ActivityManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:38.147]  	[DEBUG]		[NotesManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:38.217]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:38.234]  	[DEBUG]		[PhoneNumbersDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:38.241]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:26:38.277]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:38.284]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:38.295]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:38.309]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:38.323]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:38.334]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:38.349]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 1, Time since interaction: 3697.8141ms
[2025-08-09 14:26:38.365]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:38.410]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 1, Time since interaction: 3746.1714ms
[2025-08-09 14:26:38.417]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:38.451]  	[DEBUG]		[PhoneNumbersDialogViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 2, Time since interaction: 3730.7216ms
[2025-08-09 14:26:38.459]  	[DEBUG]		[PhoneNumbersDialogViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:38.466]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 1, Time since interaction: 3795.02ms
[2025-08-09 14:26:38.474]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:38.582]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 3, Time since interaction: 3867.23ms
[2025-08-09 14:26:38.589]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:38.638]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 3, Time since interaction: 3958.1802ms
[2025-08-09 14:26:38.649]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:38.660]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 7, Time since interaction: 3972.7856ms
[2025-08-09 14:26:38.666]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:38.813]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 2, Time since interaction: 4117.726ms
[2025-08-09 14:26:38.831]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:38.852]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 6, Time since interaction: 4150.0299ms
[2025-08-09 14:26:38.859]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:38.865]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 1, Time since interaction: 4138.0414ms
[2025-08-09 14:26:38.873]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:39.003]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 6, Time since interaction: 4294.2287ms
[2025-08-09 14:26:39.010]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:26:39.054]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 3, Time since interaction: 4418.7317ms
[2025-08-09 14:26:39.063]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
