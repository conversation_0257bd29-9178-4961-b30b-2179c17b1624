using System;
using System.Globalization;
using System.Text.RegularExpressions;
using System.Windows.Controls;
using System.Windows.Input;

namespace UFU2.Common.Extensions
{
    /// <summary>
    /// Extension methods for TextBox controls providing formatting functionality.
    /// Supports uppercase formatting for names, DD/MM/YYYY date formatting with "x" placeholders,
    /// and UFU2-specific phone number formatting.
    /// UI PROTOTYPE VERSION - Simplified implementation without backend dependencies.
    /// </summary>
    public static class TextBoxExtensions
    {
        #region Private Fields

        // Compiled regex patterns for performance
        private static readonly Regex DigitsOnlyRegex = new Regex(@"\D", RegexOptions.Compiled);
        private static readonly Regex PhoneDigitsRegex = new Regex(@"[^\d]", RegexOptions.Compiled);
        private static readonly Regex DateInputRegex = new Regex(@"[^\dx/]", RegexOptions.Compiled | RegexOptions.IgnoreCase);
        private static readonly Regex LatinCharactersOnlyRegex = new Regex(@"[^A-Za-z\s]", RegexOptions.Compiled);

        #endregion

        #region Uppercase Formatting

        /// <summary>
        /// Attaches real-time uppercase formatting to a TextBox.
        /// Converts text to uppercase as the user types.
        /// </summary>
        /// <param name="textBox">The TextBox to attach formatting to</param>
        public static void AttachUppercaseFormatting(TextBox textBox)
        {
            if (textBox == null)
                return;

            // Handle text input for real-time uppercase conversion
            textBox.TextChanged += (sender, e) =>
            {
                if (sender is TextBox tb && !string.IsNullOrEmpty(tb.Text))
                {
                    var currentPosition = tb.CaretIndex;
                    var upperText = tb.Text.ToUpper();

                    if (tb.Text != upperText)
                    {
                        tb.Text = upperText;
                        tb.CaretIndex = Math.Min(currentPosition, tb.Text.Length);
                    }
                }
            };

            // Handle paste operations
            textBox.PreviewKeyDown += (sender, e) =>
            {
                if (e.Key == Key.V && (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control)
                {
                    // Let the paste happen, then format it
                    textBox.Dispatcher.BeginInvoke(new Action(() =>
                    {
                        if (sender is TextBox tb && !string.IsNullOrEmpty(tb.Text))
                        {
                            var currentPosition = tb.CaretIndex;
                            tb.Text = tb.Text.ToUpper();
                            tb.CaretIndex = Math.Min(currentPosition, tb.Text.Length);
                        }
                    }));
                }
            };
        }

        #endregion

        #region Latin Character Validation with Uppercase Formatting

        /// <summary>
        /// Attaches Latin character validation with uppercase formatting to a TextBox.
        /// Restricts input to Latin alphabet characters (A-Z, a-z) and spaces only.
        /// Prevents Arabic characters, numbers, and special symbols while maintaining uppercase conversion.
        /// Provides real-time input filtering and validation feedback.
        /// </summary>
        /// <param name="textBox">The TextBox to attach Latin character validation to</param>
        public static void AttachLatinCharacterValidation(TextBox textBox)
        {
            if (textBox == null)
                return;

            // Flag to prevent recursive formatting events
            bool isFormatting = false;

            // Handle text input for real-time Latin character filtering and uppercase conversion
            textBox.TextChanged += (sender, e) =>
            {
                if (isFormatting || sender is not TextBox tb)
                    return;

                try
                {
                    isFormatting = true;
                    var currentPosition = tb.CaretIndex;
                    var originalText = tb.Text ?? string.Empty;

                    // Filter out non-Latin characters (keep only A-Z, a-z, and spaces)
                    var filteredText = LatinCharactersOnlyRegex.Replace(originalText, "");

                    // Convert to uppercase
                    var upperText = filteredText.ToUpper();

                    // Update text if it changed
                    if (tb.Text != upperText)
                    {
                        tb.Text = upperText;
                        // Adjust cursor position to account for filtered characters
                        var newPosition = Math.Min(currentPosition, tb.Text.Length);
                        tb.CaretIndex = newPosition;
                    }
                }
                catch
                {
                    // Silently handle any formatting errors
                }
                finally
                {
                    isFormatting = false;
                }
            };

            // Handle paste operations
            textBox.PreviewKeyDown += (sender, e) =>
            {
                if (e.Key == Key.V && (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control)
                {
                    // Let the paste happen, then filter and format it
                    textBox.Dispatcher.BeginInvoke(new Action(() =>
                    {
                        if (sender is TextBox tb && !string.IsNullOrEmpty(tb.Text))
                        {
                            var currentPosition = tb.CaretIndex;
                            var filteredText = LatinCharactersOnlyRegex.Replace(tb.Text, "");
                            var upperText = filteredText.ToUpper();

                            if (tb.Text != upperText)
                            {
                                tb.Text = upperText;
                                tb.CaretIndex = Math.Min(currentPosition, tb.Text.Length);
                            }
                        }
                    }));
                }
            };

            // Handle direct character input to prevent invalid characters from being entered
            textBox.PreviewTextInput += (sender, e) =>
            {
                // Check if the input character is a valid Latin character or space
                if (!IsValidLatinCharacter(e.Text))
                {
                    e.Handled = true; // Prevent the character from being entered
                }
            };
        }

        /// <summary>
        /// Validates if the input text contains only Latin characters and spaces.
        /// </summary>
        /// <param name="input">The input text to validate</param>
        /// <returns>True if input contains only Latin characters and spaces, false otherwise</returns>
        public static bool IsValidLatinText(string input)
        {
            if (string.IsNullOrEmpty(input))
                return true; // Empty text is considered valid

            try
            {
                // Check if the text contains only Latin characters and spaces
                return !LatinCharactersOnlyRegex.IsMatch(input);
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Validates if a single character is a valid Latin character or space.
        /// </summary>
        /// <param name="character">The character to validate</param>
        /// <returns>True if character is Latin (A-Z, a-z) or space, false otherwise</returns>
        private static bool IsValidLatinCharacter(string character)
        {
            if (string.IsNullOrEmpty(character))
                return false;

            try
            {
                // Allow Latin letters and spaces
                foreach (char c in character)
                {
                    if (!((c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z') || c == ' '))
                    {
                        return false;
                    }
                }
                return true;
            }
            catch
            {
                return false;
            }
        }

        #endregion

        #region Date Formatting

        /// <summary>
        /// Attaches DD/MM/YYYY date formatting to a TextBox with support for "x" placeholders.
        /// Supports both regular dates (27/07/2019) and special "x" dates (xx/xx/2019).
        /// </summary>
        /// <param name="textBox">The TextBox to attach date formatting to</param>
        public static void AttachDateFormatting(TextBox textBox)
        {
            if (textBox == null)
                return;

            // Handle text input for real-time date formatting
            textBox.TextChanged += (sender, e) =>
            {
                if (sender is TextBox tb && !string.IsNullOrWhiteSpace(tb.Text))
                {
                    var currentPosition = tb.CaretIndex;
                    var formattedText = FormatDateInput(tb.Text);

                    if (tb.Text != formattedText)
                    {
                        tb.Text = formattedText;
                        tb.CaretIndex = Math.Min(currentPosition, tb.Text.Length);
                    }
                }
            };

            // Handle focus loss for final formatting
            textBox.LostFocus += (sender, e) =>
            {
                if (sender is TextBox tb && !string.IsNullOrWhiteSpace(tb.Text))
                {
                    tb.Text = FormatDateInput(tb.Text);
                }
            };

            // Handle paste operations
            textBox.PreviewKeyDown += (sender, e) =>
            {
                if (e.Key == Key.V && (Keyboard.Modifiers & ModifierKeys.Control) == ModifierKeys.Control)
                {
                    // Let the paste happen, then format it
                    textBox.Dispatcher.BeginInvoke(new Action(() =>
                    {
                        if (sender is TextBox tb && !string.IsNullOrWhiteSpace(tb.Text))
                        {
                            var currentPosition = tb.CaretIndex;
                            tb.Text = FormatDateInput(tb.Text);
                            tb.CaretIndex = Math.Min(currentPosition, tb.Text.Length);
                        }
                    }));
                }
            };
        }

        /// <summary>
        /// Formats date input according to UFU2 requirements.
        /// Supports regular dates (27/07/2019) and special "x" dates (xx/xx/2019).
        /// </summary>
        /// <param name="input">The input string to format</param>
        /// <returns>Formatted date string</returns>
        private static string FormatDateInput(string input)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(input))
                    return string.Empty;

                var trimmed = input.Trim();

                // Check for placeholder year format (x1990, xx1990, etc.)
                var placeholderMatch = Regex.Match(trimmed, @"^[xX]+(\d{4})$", RegexOptions.IgnoreCase);
                if (placeholderMatch.Success)
                {
                    var year = placeholderMatch.Groups[1].Value;
                    if (int.TryParse(year, out int yearInt) && yearInt >= 1900 && yearInt <= 2100)
                    {
                        return $"xx/xx/{year}";
                    }
                }

                // Remove invalid characters (keep only digits, x, and /)
                var cleaned = DateInputRegex.Replace(trimmed, "");

                // Handle different input patterns
                if (cleaned.Contains("x"))
                {
                    // Handle mixed x and digits
                    return FormatMixedDateInput(cleaned);
                }
                else
                {
                    // Handle pure numeric input
                    return FormatNumericDateInput(cleaned);
                }
            }
            catch
            {
                return input; // Return original on error
            }
        }

        /// <summary>
        /// Formats mixed date input containing both "x" and digits.
        /// </summary>
        /// <param name="input">Input containing x and digits</param>
        /// <returns>Formatted date string</returns>
        private static string FormatMixedDateInput(string input)
        {
            // For now, if it contains x, try to extract year and format as xx/xx/year
            var yearMatch = Regex.Match(input, @"(\d{4})");
            if (yearMatch.Success)
            {
                return $"xx/xx/{yearMatch.Groups[1].Value}";
            }
            return input;
        }

        /// <summary>
        /// Formats numeric date input (digits only).
        /// Only formats when exactly 8 digits are entered to prevent premature formatting.
        /// </summary>
        /// <param name="input">Numeric input string</param>
        /// <returns>Formatted date string</returns>
        private static string FormatNumericDateInput(string input)
        {
            var digitsOnly = DigitsOnlyRegex.Replace(input, "");

            // Only auto-format when exactly 8 digits (DDMMYYYY) - prevents premature formatting
            if (digitsOnly.Length == 8)
            {
                var day = digitsOnly.Substring(0, 2);
                var month = digitsOnly.Substring(2, 2);
                var year = digitsOnly.Substring(4, 4);

                if (IsValidDate(day, month, year))
                {
                    return $"{day}/{month}/{year}";
                }
            }

            // For other lengths, return digits only without any formatting
            // This prevents premature conversion like "272" -> "27/2"
            return digitsOnly;
        }
        /// <summary>
        /// Validates if the given day, month, and year form a valid date.
        /// </summary>
        /// <param name="day">Day string</param>
        /// <param name="month">Month string</param>
        /// <param name="year">Year string</param>
        /// <returns>True if valid date, false otherwise</returns>
        private static bool IsValidDate(string day, string month, string year)
        {
            try
            {
                if (int.TryParse(day, out var d) &&
                    int.TryParse(month, out var m) &&
                    int.TryParse(year, out var y))
                {
                    var date = new DateTime(y, m, d);
                    return date.Year == y && date.Month == m && date.Day == d;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
        #endregion

        #region Phone Number Formatting

        /// <summary>
        /// Attaches UFU2 phone number formatting to a TextBox.
        /// Formats 9-digit numbers as XXX-XX-XX-XX and 10-digit numbers as XXXX-XX-XX-XX.
        /// Auto-converts when exactly 9 digits are entered.
        /// Optimized to prevent excessive formatting events.
        /// </summary>
        /// <param name="textBox">The TextBox to attach phone formatting to</param>
        public static void AttachPhoneNumberFormatting(TextBox textBox)
        {
            if (textBox == null)
                return;

            // Flag to prevent recursive formatting events
            bool isFormatting = false;

            // Handle text input for real-time phone number formatting
            textBox.TextChanged += (sender, e) =>
            {
                if (isFormatting || sender is not TextBox tb || string.IsNullOrWhiteSpace(tb.Text))
                    return;

                try
                {
                    var currentPosition = tb.CaretIndex;
                    var digitsOnly = PhoneDigitsRegex.Replace(tb.Text, "");

                    // Auto-format when exactly 9 or 10 digits are entered
                    if (digitsOnly.Length == 9 || digitsOnly.Length == 10)
                    {
                        var formatted = FormatPhoneNumber(digitsOnly);
                        if (tb.Text != formatted)
                        {
                            isFormatting = true;
                            tb.Text = formatted;
                            tb.CaretIndex = tb.Text.Length; // Move cursor to end
                            isFormatting = false;
                        }
                    }
                }
                catch
                {
                    isFormatting = false; // Reset flag on error
                }
            };
            // Handle focus loss for final formatting
            textBox.LostFocus += (sender, e) =>
            {
                if (sender is TextBox tb && !string.IsNullOrWhiteSpace(tb.Text))
                {
                    var digitsOnly = PhoneDigitsRegex.Replace(tb.Text, "");
                    var formatted = FormatPhoneNumber(digitsOnly);
                    if (tb.Text != formatted)
                    {
                        tb.Text = formatted;
                    }
                }
            };
        }

        /// <summary>
        /// Formats a phone number according to UFU2 requirements.
        /// 9 digits: XXX-XX-XX-XX, 10 digits: XXXX-XX-XX-XX
        /// </summary>
        /// <param name="digits">String containing only digits</param>
        /// <returns>Formatted phone number</returns>
        private static string FormatPhoneNumber(string digits)
        {
            if (string.IsNullOrWhiteSpace(digits))
                return string.Empty;

            try
            {
                return digits.Length switch
                {
                    9 => $"{digits.Substring(0, 3)}-{digits.Substring(3, 2)}-{digits.Substring(5, 2)}-{digits.Substring(7, 2)}",
                    10 => $"{digits.Substring(0, 4)}-{digits.Substring(4, 2)}-{digits.Substring(6, 2)}-{digits.Substring(8, 2)}",
                    _ => digits // Return original if not 9 or 10 digits
                };
            }
            catch
            {
                return digits; // Return original on error
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Extracts only digits from a string, removing all non-digit characters.
        /// </summary>
        /// <param name="input">The input string to extract digits from</param>
        /// <returns>String containing only digits</returns>
        private static string ExtractDigitsOnly(string input)
        {
            if (string.IsNullOrWhiteSpace(input))
                return string.Empty;

            try
            {
                return DigitsOnlyRegex.Replace(input, "");
            }
            catch
            {
                return string.Empty;
            }
        }

        #endregion
    }
}
