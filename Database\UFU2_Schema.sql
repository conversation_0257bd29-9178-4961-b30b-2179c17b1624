-- UFU2 Database Schema Foundation - Version 1
-- Complete database schema with PascalCase naming convention
-- Includes CHECK constraints, foreign keys, and strategic indexing
-- Supports Arabic RTL text and UFU2 business requirements

-- Enable foreign key constraints
PRAGMA foreign_keys = ON;

-- ============================================================================
-- CORE TABLES
-- ============================================================================

-- 1. Clients - Main client information table
CREATE TABLE IF NOT EXISTS Clients (
    Uid TEXT PRIMARY KEY,
    NameFr TEXT NOT NULL,
    NameAr TEXT,
    BirthDate TEXT,
    BirthPlace TEXT,
    Gender INTEGER DEFAULT 0,
    Address TEXT,
    NationalId TEXT,
    CreatedAt TEXT DEFAULT (strftime('%d/%m/%Y %H:%M:%S', 'now', 'localtime')),
    UpdatedAt TEXT DEFAULT (strftime('%d/%m/%Y %H:%M:%S', 'now', 'localtime')),

    -- Data validation constraints
    CONSTRAINT chk_clients_gender CHECK (Gender IS NULL OR Gender IN (0, 1)),
    CONSTRAINT chk_clients_name_fr_not_empty CHECK (length(trim(NameFr)) > 0)
    -- NationalId format constraint removed to allow flexible input as per database binding requirements
);

-- 2. PhoneNumbers - Client phone numbers with type classification
CREATE TABLE IF NOT EXISTS PhoneNumbers (
    Uid TEXT PRIMARY KEY,
    ClientUid TEXT NOT NULL,
    PhoneNumber TEXT,
    PhoneType INTEGER DEFAULT 0,
    IsPrimary INTEGER DEFAULT 0,

    -- Foreign key relationships
    FOREIGN KEY (ClientUid) REFERENCES Clients(Uid) ON DELETE CASCADE,

    -- Data validation constraints
    CONSTRAINT chk_phone_type CHECK (PhoneType IS NULL OR PhoneType IN (0, 1, 2, 3)),
    CONSTRAINT chk_phone_is_primary CHECK (IsPrimary IS NULL OR IsPrimary IN (0, 1)),
    CONSTRAINT chk_phone_number_not_empty CHECK (PhoneNumber IS NULL OR length(trim(PhoneNumber)) > 0),
    CONSTRAINT chk_phone_number_format CHECK (PhoneNumber IS NULL OR length(trim(PhoneNumber)) >= 8)
);

-- 3. Activities - Business activities linked to clients
CREATE TABLE IF NOT EXISTS Activities (
    Uid TEXT PRIMARY KEY,
    ClientUid TEXT NOT NULL,
    ActivityType TEXT,
    ActivityStatus TEXT,
    ActivityStartDate TEXT,
    CommercialRegister TEXT,
    ActivityLocation TEXT,
    NifNumber TEXT,
    NisNumber TEXT,
    ArtNumber TEXT,
    CpiDaira TEXT,
    CpiWilaya TEXT,
    ActivityUpdateDate TEXT,
    ActivityUpdateNote TEXT,
    CreatedAt TEXT DEFAULT (strftime('%d/%m/%Y %H:%M:%S', 'now', 'localtime')),
    UpdatedAt TEXT DEFAULT (strftime('%d/%m/%Y %H:%M:%S', 'now', 'localtime')),

    -- Foreign key relationships
    FOREIGN KEY (ClientUid) REFERENCES Clients(Uid) ON DELETE CASCADE,

    -- Data validation constraints
    CONSTRAINT chk_activity_type CHECK (ActivityType IS NULL OR ActivityType IN ('MainCommercial', 'SecondaryCommercial', 'Craft', 'Professional')),
    CONSTRAINT chk_nif_number_format CHECK (NifNumber IS NULL OR length(trim(NifNumber)) >= 8),
    CONSTRAINT chk_nis_number_format CHECK (NisNumber IS NULL OR length(trim(NisNumber)) >= 8),
    CONSTRAINT chk_art_number_format CHECK (ArtNumber IS NULL OR length(trim(ArtNumber)) >= 6)
);

-- ============================================================================
-- ACTIVITY-RELATED TABLES
-- ============================================================================

-- 4. CommercialActivityCodes - Activity codes for commercial businesses
CREATE TABLE IF NOT EXISTS CommercialActivityCodes (
    Uid TEXT PRIMARY KEY,
    ActivityUid TEXT NOT NULL,
    ActivityCode INTEGER NOT NULL,

    -- Foreign key relationships
    FOREIGN KEY (ActivityUid) REFERENCES Activities(Uid) ON DELETE CASCADE,

    -- Data validation constraints
    CONSTRAINT chk_activity_code_positive CHECK (ActivityCode > 0),
    CONSTRAINT chk_activity_code_range CHECK (ActivityCode >= 100000 AND ActivityCode <= 999999),

    -- Unique constraint to prevent duplicate codes per activity
    CONSTRAINT uq_activity_code_per_activity UNIQUE (ActivityUid, ActivityCode)
);

-- 5. ProfessionNames - Text descriptions for craft/professional activities
CREATE TABLE IF NOT EXISTS ProfessionNames (
    ActivityUid TEXT PRIMARY KEY,
    ActivityDescription TEXT,

    -- Foreign key relationships
    FOREIGN KEY (ActivityUid) REFERENCES Activities(Uid) ON DELETE CASCADE,

    -- Data validation constraints
    CONSTRAINT chk_activity_description_not_empty CHECK (ActivityDescription IS NULL OR length(trim(ActivityDescription)) > 0)
);

-- 6. CraftActivityCodes - Craft codes for craft activities (one-to-one with activities)
CREATE TABLE IF NOT EXISTS CraftActivityCodes (
    Uid TEXT PRIMARY KEY,
    ActivityUid TEXT NOT NULL,
    CraftCode TEXT NOT NULL,

    -- Foreign key relationships
    FOREIGN KEY (ActivityUid) REFERENCES Activities(Uid) ON DELETE CASCADE,

    -- Data validation constraints
    CONSTRAINT chk_craft_code_format CHECK (CraftCode GLOB '[0-9][0-9]-[0-9][0-9]-[0-9][0-9][0-9]'),

    -- One-to-one constraint: each activity can have only one craft code
    CONSTRAINT uq_craft_activity_uid UNIQUE (ActivityUid)
);

-- 7. FileCheckStates - Document verification status per activity type
CREATE TABLE IF NOT EXISTS FileCheckStates (
    Uid TEXT PRIMARY KEY,
    ActivityUid TEXT NOT NULL,
    FileCheckType TEXT NOT NULL,
    IsChecked INTEGER DEFAULT 0,
    CheckedDate TEXT,

    -- Foreign key relationships
    FOREIGN KEY (ActivityUid) REFERENCES Activities(Uid) ON DELETE CASCADE,

    -- Data validation constraints
    CONSTRAINT chk_file_check_type CHECK (FileCheckType IN ('CAS', 'NIF', 'NIS', 'RC', 'ART', 'AGR', 'DEX')),
    CONSTRAINT chk_file_is_checked CHECK (IsChecked IS NULL OR IsChecked IN (0, 1)),

    -- Unique constraint to prevent duplicate file check types per activity
    CONSTRAINT uq_file_check_per_activity UNIQUE (ActivityUid, FileCheckType)
);

-- 8. Notes - Activity-specific notes with a priority system
CREATE TABLE IF NOT EXISTS Notes (
    Uid TEXT PRIMARY KEY,
    ActivityUid TEXT NOT NULL,
    Content TEXT,
    Priority INTEGER DEFAULT 0,
    CreatedAt TEXT DEFAULT (strftime('%d/%m/%Y %H:%M:%S', 'now', 'localtime')),
    UpdatedAt TEXT DEFAULT (strftime('%d/%m/%Y %H:%M:%S', 'now', 'localtime')),

    -- Foreign key relationships
    FOREIGN KEY (ActivityUid) REFERENCES Activities(Uid) ON DELETE CASCADE,

    -- Data validation constraints
    CONSTRAINT chk_note_priority CHECK (Priority IS NULL OR Priority IN (0, 1, 2)),
    CONSTRAINT chk_note_content_not_empty CHECK (Content IS NULL OR length(trim(Content)) > 0)
);

-- 9. G12Check - G12 payment year tracking
CREATE TABLE IF NOT EXISTS G12Check (
    Uid TEXT PRIMARY KEY,
    ActivityUid TEXT NOT NULL,
    Year INTEGER NOT NULL,

    -- Foreign key relationships
    FOREIGN KEY (ActivityUid) REFERENCES Activities(Uid) ON DELETE CASCADE,

    -- Data validation constraints
    CONSTRAINT chk_g12_year_range CHECK (Year >= 2000 AND Year <= 2100),

    -- Unique constraint to prevent duplicate years per activity
    CONSTRAINT uq_g12_year_per_activity UNIQUE (ActivityUid, Year)
);

-- 10. BisCheck - BIS payment year tracking
CREATE TABLE IF NOT EXISTS BisCheck (
    Uid TEXT PRIMARY KEY,
    ActivityUid TEXT NOT NULL,
    Year INTEGER NOT NULL,

    -- Foreign key relationships
    FOREIGN KEY (ActivityUid) REFERENCES Activities(Uid) ON DELETE CASCADE,

    -- Data validation constraints
    CONSTRAINT chk_bis_year_range CHECK (Year >= 2000 AND Year <= 2100),

    -- Unique constraint to prevent duplicate years per activity
    CONSTRAINT uq_bis_year_per_activity UNIQUE (ActivityUid, Year)
);

-- ============================================================================
-- UID GENERATION SUPPORT
-- ============================================================================

-- 11. UidSequences - UID generation sequence tracking
CREATE TABLE IF NOT EXISTS UidSequences (
    EntityType TEXT NOT NULL,
    Prefix TEXT NOT NULL,
    LastSequence INTEGER DEFAULT 0,
    UpdatedAt TEXT DEFAULT (strftime('%d/%m/%Y %H:%M:%S', 'now', 'localtime')),

    -- Composite primary key
    PRIMARY KEY (EntityType, Prefix),

    -- Data validation constraints
    CONSTRAINT chk_entity_type CHECK (EntityType IN ('Client', 'Activity', 'Note', 'CommercialActivityCode', 'CraftActivityCode', 'FileCheckState', 'G12Check', 'BisCheck')),
    CONSTRAINT chk_last_sequence_non_negative CHECK (LastSequence >= 0)
);

-- ============================================================================
-- STRATEGIC INDEXING
-- ============================================================================

-- Core search indexes for client name fields
CREATE INDEX IF NOT EXISTS idx_clients_name_fr ON Clients(NameFr);
CREATE INDEX IF NOT EXISTS idx_clients_name_ar ON Clients(NameAr);
CREATE INDEX IF NOT EXISTS idx_clients_name_fr_lower ON Clients(lower(NameFr));

-- Foreign key indexes for efficient JOIN operations
CREATE INDEX IF NOT EXISTS idx_phone_numbers_client_uid ON PhoneNumbers(ClientUid);
CREATE INDEX IF NOT EXISTS idx_activities_client_uid ON Activities(ClientUid);
CREATE INDEX IF NOT EXISTS idx_commercial_activity_codes_activity_uid ON CommercialActivityCodes(ActivityUid);
CREATE INDEX IF NOT EXISTS idx_profession_names_activity_uid ON ProfessionNames(ActivityUid);
CREATE INDEX IF NOT EXISTS idx_craft_activity_codes_activity_uid ON CraftActivityCodes(ActivityUid);
CREATE INDEX IF NOT EXISTS idx_craft_activity_codes_craft_code ON CraftActivityCodes(CraftCode);
CREATE INDEX IF NOT EXISTS idx_file_check_states_activity_uid ON FileCheckStates(ActivityUid);
CREATE INDEX IF NOT EXISTS idx_notes_activity_uid ON Notes(ActivityUid);
CREATE INDEX IF NOT EXISTS idx_g12_check_activity_uid ON G12Check(ActivityUid);
CREATE INDEX IF NOT EXISTS idx_bis_check_activity_uid ON BisCheck(ActivityUid);

-- Conditional indexes for sparse data filtering
CREATE INDEX IF NOT EXISTS idx_clients_national_id ON Clients(NationalId) WHERE NationalId IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_activities_type ON Activities(ActivityType) WHERE ActivityType IS NOT NULL;

-- Unique constraint indexes for business rule enforcement
-- Modified to only enforce uniqueness for non-empty NationalId values
CREATE UNIQUE INDEX IF NOT EXISTS idx_clients_national_id_unique ON Clients(NationalId) WHERE NationalId IS NOT NULL AND length(trim(NationalId)) > 0;
CREATE UNIQUE INDEX IF NOT EXISTS idx_phone_numbers_primary_per_client ON PhoneNumbers(ClientUid) WHERE IsPrimary = 1;

-- Performance indexes for common query patterns
CREATE INDEX IF NOT EXISTS idx_activities_nif_number ON Activities(NifNumber) WHERE NifNumber IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_activities_nis_number ON Activities(NisNumber) WHERE NisNumber IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_activities_art_number ON Activities(ArtNumber) WHERE ArtNumber IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_file_check_states_type_checked ON FileCheckStates(FileCheckType, IsChecked);
CREATE INDEX IF NOT EXISTS idx_notes_priority ON Notes(Priority);

-- Temporal indexes for date-based queries
CREATE INDEX IF NOT EXISTS idx_clients_created_at ON Clients(CreatedAt);
CREATE INDEX IF NOT EXISTS idx_activities_created_at ON Activities(CreatedAt);
CREATE INDEX IF NOT EXISTS idx_activities_start_date ON Activities(ActivityStartDate) WHERE ActivityStartDate IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_g12_check_year ON G12Check(Year);
CREATE INDEX IF NOT EXISTS idx_bis_check_year ON BisCheck(Year);

-- ============================================================================
-- HIGH-PRIORITY COMPOSITE INDEXES (Version 3)
-- ============================================================================

-- Composite Arabic Search Index for improved Arabic name searches
CREATE INDEX IF NOT EXISTS idx_clients_arabic_search
ON Clients(NameAr, NationalId, Address)
WHERE NameAr IS NOT NULL;

-- Activity Type and Client Composite Index for improved activity queries
CREATE INDEX IF NOT EXISTS idx_activities_type_client
ON Activities(ActivityType, ClientUid, CreatedAt)
WHERE ActivityType IS NOT NULL;

-- Phone Number Composite Index for improved contact operations
CREATE INDEX IF NOT EXISTS idx_phone_numbers_composite
ON PhoneNumbers(ClientUid, IsPrimary, PhoneType);

-- ============================================================================
-- MEDIUM-PRIORITY COMPOSITE INDEXES (Version 4)
-- ============================================================================

-- Notes Priority + Content Composite Index
CREATE INDEX IF NOT EXISTS idx_notes_priority_content
ON Notes(ActivityUid, Priority, CreatedAt)
WHERE Priority IS NOT NULL;

-- File Check States Optimization Index
CREATE INDEX IF NOT EXISTS idx_file_check_status_activity
ON FileCheckStates(IsChecked, ActivityUid, FileCheckType);

-- Activity Date Range Composite Index
CREATE INDEX IF NOT EXISTS idx_activities_date_range
ON Activities(ActivityStartDate, CreatedAt, ActivityType)
WHERE ActivityStartDate IS NOT NULL;

-- Client Address Search Enhancement Index
CREATE INDEX IF NOT EXISTS idx_clients_address_search
ON Clients(Address, NameAr, NationalId)
WHERE Address IS NOT NULL AND length(trim(Address)) > 0;

-- Activity Update Tracking Index
CREATE INDEX IF NOT EXISTS idx_activities_update_tracking
ON Activities(ClientUid, UpdatedAt, ActivityUpdateDate)
WHERE ActivityUpdateDate IS NOT NULL;

-- Phone Number Type Optimization Index
CREATE INDEX IF NOT EXISTS idx_phone_numbers_type_optimization
ON PhoneNumbers(PhoneType, IsPrimary, ClientUid);

-- ============================================================================
-- SCHEMA VERSION TRACKING
-- ============================================================================

-- Schema version table for migration management
CREATE TABLE IF NOT EXISTS SchemaVersion (
    Version INTEGER PRIMARY KEY,
    AppliedAt TEXT DEFAULT (strftime('%d/%m/%Y %H:%M:%S', 'now', 'localtime')),
    Description TEXT
);

-- Insert initial schema version
INSERT OR IGNORE INTO SchemaVersion (Version, Description)
VALUES (4, 'UFU2 database schema with enhanced performance indexes');

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- ============================================================================

-- Update timestamp triggers for Clients table
CREATE TRIGGER IF NOT EXISTS trg_clients_updated_at
    AFTER UPDATE ON Clients
    FOR EACH ROW
BEGIN
    UPDATE Clients SET UpdatedAt = strftime('%d/%m/%Y %H:%M:%S', 'now', 'localtime') WHERE Uid = NEW.Uid;
END;

-- Update timestamp triggers for Activities table
CREATE TRIGGER IF NOT EXISTS trg_activities_updated_at
    AFTER UPDATE ON Activities
    FOR EACH ROW
BEGIN
    UPDATE Activities SET UpdatedAt = strftime('%d/%m/%Y %H:%M:%S', 'now', 'localtime') WHERE Uid = NEW.Uid;
END;

-- Update timestamp triggers for Notes table
CREATE TRIGGER IF NOT EXISTS trg_notes_updated_at
    AFTER UPDATE ON Notes
    FOR EACH ROW
BEGIN
    UPDATE Notes SET UpdatedAt = strftime('%d/%m/%Y %H:%M:%S', 'now', 'localtime') WHERE Uid = NEW.Uid;
END;

-- ============================================================================
-- BUSINESS RULE VALIDATION TRIGGERS
-- ============================================================================

-- Trigger to ensure only one primary phone per client
CREATE TRIGGER IF NOT EXISTS trg_phone_numbers_primary_validation
    BEFORE INSERT ON PhoneNumbers
    FOR EACH ROW
    WHEN NEW.IsPrimary = 1
BEGIN
    UPDATE PhoneNumbers SET IsPrimary = 0 WHERE ClientUid = NEW.ClientUid AND IsPrimary = 1;
END;

-- File check type validation trigger
-- Ensures only valid file check types are inserted based on an activity type
CREATE TRIGGER IF NOT EXISTS trg_file_check_states_validation
    BEFORE INSERT ON FileCheckStates
    FOR EACH ROW
    WHEN (
        SELECT CASE 
            WHEN a.ActivityType IN ('MainCommercial', 'SecondaryCommercial') THEN
                NEW.FileCheckType NOT IN ('CAS', 'NIF', 'NIS', 'RC', 'DEX')
            WHEN a.ActivityType = 'Craft' THEN
                NEW.FileCheckType NOT IN ('CAS', 'NIF', 'NIS', 'ART', 'DEX')
            WHEN a.ActivityType = 'Professional' THEN
                NEW.FileCheckType NOT IN ('CAS', 'NIF', 'NIS', 'AGR', 'DEX')
            ELSE
                NEW.FileCheckType NOT IN ('CAS', 'NIF', 'NIS', 'DEX')
        END
        FROM Activities a 
        WHERE a.Uid = NEW.ActivityUid
    )
BEGIN
    SELECT RAISE(ABORT, 'Invalid file check type for activity type');
END;

-- File check type validation trigger for updates
-- Ensures only valid file check types are updated based on an activity type
CREATE TRIGGER IF NOT EXISTS trg_file_check_states_update_validation
    BEFORE UPDATE ON FileCheckStates
    FOR EACH ROW
    WHEN (
        SELECT CASE 
            WHEN a.ActivityType IN ('MainCommercial', 'SecondaryCommercial') THEN
                NEW.FileCheckType NOT IN ('CAS', 'NIF', 'NIS', 'RC', 'DEX')
            WHEN a.ActivityType = 'Craft' THEN
                NEW.FileCheckType NOT IN ('CAS', 'NIF', 'NIS', 'ART', 'DEX')
            WHEN a.ActivityType = 'Professional' THEN
                NEW.FileCheckType NOT IN ('CAS', 'NIF', 'NIS', 'AGR', 'DEX')
            ELSE
                NEW.FileCheckType NOT IN ('CAS', 'NIF', 'NIS', 'DEX')
        END
        FROM Activities a 
        WHERE a.Uid = NEW.ActivityUid
    )
BEGIN
    SELECT RAISE(ABORT, 'Invalid file check type for activity type');
END;

-- ============================================================================
-- INITIAL DATA SETUP
-- ============================================================================

-- Initialize UID sequences for common prefixes (optional, will be created as needed)
-- This is just for demonstration - actual sequences will be created dynamically

-- ============================================================================
-- SCHEMA VALIDATION QUERIES
-- ============================================================================

-- The following queries can be used to validate the schema implementation:

-- 1. Verify all tables exist
-- SELECT name FROM sqlite_master WHERE type='table' ORDER BY name;

-- 2. Verify all indexes exist
-- SELECT name FROM sqlite_master WHERE type='index' ORDER BY name;

-- 3. Verify foreign key relationships
-- PRAGMA foreign_key_list(Clients);
-- PRAGMA foreign_key_list(PhoneNumbers);
-- PRAGMA foreign_key_list(Activities);

-- 4. Verify table structure
-- PRAGMA table_info(Clients);
-- PRAGMA table_info(Activities);



-- ============================================================================
-- END OF SCHEMA
-- ============================================================================