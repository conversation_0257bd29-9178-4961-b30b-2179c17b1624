using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Data.Sqlite;
using Dapper;
using UFU2.Common;
using UFU2.Models;

namespace UFU2.Services
{
    /// <summary>
    /// Comprehensive database validation service for UFU2 application.
    /// Provides validation for database operations, business rules, and data integrity.
    /// Integrates with existing UFU2 error handling and logging patterns.
    /// </summary>
    public class DatabaseValidationService : IDisposable
    {
        #region Constants

        // Activity types
        private const string ActivityTypeMainCommercial = "MainCommercial";
        private const string ActivityTypeSecondaryCommercial = "SecondaryCommercial";
        private const string ActivityTypeCraft = "Craft";
        private const string ActivityTypeProfessional = "Professional";

        // File check types
        private const string FileCheckTypeCAS = "CAS";
        private const string FileCheckTypeNIF = "NIF";
        private const string FileCheckTypeNIS = "NIS";
        private const string FileCheckTypeRC = "RC";
        private const string FileCheckTypeART = "ART";
        private const string FileCheckTypeAGR = "AGR";
        private const string FileCheckTypeDEX = "DEX";

        // Validation thresholds
        private const int MinPhoneNumberLength = 8;
        private const int MinValidPaymentYear = 2000;
        private const int ConcurrentUIDTestCount = 5;

        #endregion

        #region Private Fields

        private readonly DatabaseService _databaseService;
        private readonly UIDGenerationService _uidGenerationService;
        private readonly DatabaseSchemaValidator _schemaValidator;
        private bool _disposed = false;

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the DatabaseValidationService.
        /// </summary>
        /// <param name="databaseService">The database service instance</param>
        /// <param name="uidGenerationService">The UID generation service instance</param>
        /// <param name="schemaValidator">The schema validator instance</param>
        public DatabaseValidationService(
            DatabaseService databaseService,
            UIDGenerationService uidGenerationService,
            DatabaseSchemaValidator schemaValidator)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _uidGenerationService = uidGenerationService ?? throw new ArgumentNullException(nameof(uidGenerationService));
            _schemaValidator = schemaValidator ?? throw new ArgumentNullException(nameof(schemaValidator));

            LoggingService.LogInfo("DatabaseValidationService initialized", "DatabaseValidationService");
        }

        #endregion

        #region UID Generation Validation

        /// <summary>
        /// Validates UID generation with uniqueness and format checking.
        /// </summary>
        /// <returns>UID validation result</returns>
        public async Task<UIDValidationResult> ValidateUIDGenerationAsync()
        {
            var result = new UIDValidationResult();

            try
            {
                LoggingService.LogInfo("Starting UID generation validation", "DatabaseValidationService");

                // Validate UID format compliance
                await ValidateUIDFormatsAsync(result);

                // Validate UID uniqueness
                await ValidateUIDUniquenessAsync(result);

                // Validate UID sequence integrity
                await ValidateUIDSequenceIntegrityAsync(result);

                // Test concurrent UID generation
                await ValidateConcurrentUIDGenerationAsync(result);

                result.IsValid = !result.Errors.Any();
                result.ValidationTime = DateTime.UtcNow;

                var status = result.IsValid ? "PASSED" : "FAILED";
                LoggingService.LogInfo($"UID validation {status}: {result.Errors.Count} errors, {result.Warnings.Count} warnings", "DatabaseValidationService");

                return result;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"UID validation failed with exception: {ex.Message}");
                result.IsValid = false;
                result.ValidationTime = DateTime.UtcNow;

                ErrorManager.HandleErrorToast(ex, "فشل في التحقق من توليد المعرفات الفريدة", "خطأ في التحقق", LogLevel.Error, "DatabaseValidationService");
                return result;
            }
        }

        /// <summary>
        /// Validates UID format compliance across all entities.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateUIDFormatsAsync(UIDValidationResult result)
        {
            SqliteConnection? connection = null;
            try
            {
                connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Validate Client UID formats
                var invalidClientUIDs = await connection.QueryAsync<string>(@"
                    SELECT Uid FROM Clients
                    WHERE Uid NOT GLOB '[A-Za-z][0-9]*'");

                var clientUIDList = invalidClientUIDs.ToList();
                foreach (var uid in clientUIDList)
                {
                    result.Errors.Add($"Invalid Client UID format: {uid}");
                }

                // Validate Activity UID formats (supports both standard and alternate format with 's' suffix)
                var invalidActivityUIDs = await connection.QueryAsync<string>(@"
                    SELECT Uid FROM Activities
                    WHERE Uid NOT GLOB '[A-Za-z][0-9]*_Act[0-9]*'
                    AND Uid NOT GLOB '[A-Za-z][0-9]*_Act[0-9]*s'");

                var activityUIDList = invalidActivityUIDs.ToList();
                foreach (var uid in activityUIDList)
                {
                    result.Errors.Add($"Invalid Activity UID format: {uid}");
                }

                result.ClientUIDsValidated = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM Clients");
                result.ActivityUIDsValidated = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM Activities");

                LoggingService.LogDebug($"UID format validation completed: {clientUIDList.Count + activityUIDList.Count} invalid formats found", "DatabaseValidationService");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"UID format validation failed: {ex.Message}");
                LoggingService.LogError($"UID format validation error: {ex.Message}", "DatabaseValidationService");
            }
            finally
            {
                connection?.Dispose();
            }
        }

        /// <summary>
        /// Validates UID uniqueness across all entities.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateUIDUniquenessAsync(UIDValidationResult result)
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Check for duplicate Client UIDs
                var duplicateClientUIDs = await connection.QueryAsync<dynamic>(@"
                    SELECT uid, COUNT(*) as count 
                    FROM clients 
                    GROUP BY uid 
                    HAVING COUNT(*) > 1");

                foreach (var duplicate in duplicateClientUIDs)
                {
                    result.Errors.Add($"Duplicate Client UID found: {duplicate.uid} (count: {duplicate.count})");
                }

                // Check for duplicate Activity UIDs
                var duplicateActivityUIDs = await connection.QueryAsync<dynamic>(@"
                    SELECT uid, COUNT(*) as count 
                    FROM activities 
                    GROUP BY uid 
                    HAVING COUNT(*) > 1");

                foreach (var duplicate in duplicateActivityUIDs)
                {
                    result.Errors.Add($"Duplicate Activity UID found: {duplicate.uid} (count: {duplicate.count})");
                }

                LoggingService.LogDebug($"UID uniqueness validation completed: {duplicateClientUIDs.Count() + duplicateActivityUIDs.Count()} duplicates found", "DatabaseValidationService");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"UID uniqueness validation failed: {ex.Message}");
                LoggingService.LogError($"UID uniqueness validation error: {ex.Message}", "DatabaseValidationService");
            }
        }

        /// <summary>
        /// Validates UID sequence integrity and consistency.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateUIDSequenceIntegrityAsync(UIDValidationResult result)
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Get all sequence records (using PascalCase table and column names)
                var sequences = await connection.QueryAsync<dynamic>(@"
                    SELECT EntityType, Prefix, LastSequence
                    FROM UidSequences
                    ORDER BY EntityType, Prefix");

                var sequenceList = sequences.ToList();
                result.SequencesValidated = sequenceList.Count;

                // Batch validate client sequences (using PascalCase column names)
                await ValidateClientSequencesAsync(connection, sequenceList.Where(s => s.EntityType == "Client"), result);

                // Batch validate activity sequences (using PascalCase column names)
                await ValidateActivitySequencesAsync(connection, sequenceList.Where(s => s.EntityType == "Activity"), result);

                LoggingService.LogDebug($"UID sequence integrity validation completed: {result.SequencesValidated} sequences checked", "DatabaseValidationService");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"UID sequence integrity validation failed: {ex.Message}");
                LoggingService.LogError($"UID sequence integrity validation error: {ex.Message}", "DatabaseValidationService");
            }
        }

        /// <summary>
        /// Validates client UID sequences in batch.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="clientSequences">Client sequences to validate</param>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateClientSequencesAsync(SqliteConnection connection, IEnumerable<dynamic> clientSequences, UIDValidationResult result)
        {
            var clientSequenceList = clientSequences.ToList();
            if (!clientSequenceList.Any()) return;

            // Get all client UID max sequences in one query
            var clientMaxSequences = await connection.QueryAsync<dynamic>(@"
                SELECT SUBSTR(uid, 1, 1) as prefix, MAX(CAST(SUBSTR(uid, 2) AS INTEGER)) as max_sequence
                FROM clients 
                WHERE LENGTH(uid) > 1
                GROUP BY SUBSTR(uid, 1, 1)");

            var maxSequenceDict = clientMaxSequences.ToDictionary(
                x => x.prefix?.ToString() ?? "", 
                x => (int?)x.max_sequence
            );

            foreach (var sequence in clientSequenceList)
            {
                var prefix = sequence.Prefix?.ToString() ?? "";
                if (maxSequenceDict.TryGetValue(prefix, out int? maxSequence) &&
                    maxSequence.HasValue && maxSequence.Value > sequence.LastSequence)
                {
                    result.Warnings.Add($"Client sequence for prefix '{prefix}' is behind actual data: sequence={sequence.LastSequence}, max_actual={maxSequence.Value}");
                }
            }
        }

        /// <summary>
        /// Validates activity UID sequences in batch.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="activitySequences">Activity sequences to validate</param>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateActivitySequencesAsync(SqliteConnection connection, IEnumerable<dynamic> activitySequences, UIDValidationResult result)
        {
            var activitySequenceList = activitySequences.ToList();
            if (!activitySequenceList.Any()) return;

            // Get all activity UID max sequences in one query
            var activityMaxSequences = await connection.QueryAsync<dynamic>(@"
                SELECT ClientUid, MAX(CAST(SUBSTR(Uid, INSTR(Uid, '_Act') + 4) AS INTEGER)) as max_sequence
                FROM Activities
                WHERE Uid LIKE '%_Act%'
                GROUP BY ClientUid");

            var maxSequenceDict = activityMaxSequences.ToDictionary(
                x => x.ClientUid?.ToString() ?? "",
                x => (int?)x.max_sequence
            );

            foreach (var sequence in activitySequenceList)
            {
                var clientUID = sequence.Prefix?.ToString() ?? "";
                if (maxSequenceDict.TryGetValue(clientUID, out int? maxSequence) &&
                    maxSequence.HasValue && maxSequence.Value > sequence.LastSequence)
                {
                    result.Warnings.Add($"Activity sequence for client '{clientUID}' is behind actual data: sequence={sequence.LastSequence}, max_actual={maxSequence.Value}");
                }
            }
        }

        /// <summary>
        /// Validates concurrent UID generation scenarios.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateConcurrentUIDGenerationAsync(UIDValidationResult result)
        {
            try
            {
                // Test concurrent client UID generation
                var tasks = new List<Task<string>>();
                for (int i = 0; i < ConcurrentUIDTestCount; i++)
                {
                    tasks.Add(_uidGenerationService.GenerateClientUIDAsync($"TestClient{i}"));
                }

                var generatedUIDs = await Task.WhenAll(tasks);
                
                // Check for duplicates in concurrent generation
                var duplicates = generatedUIDs.GroupBy(uid => uid).Where(g => g.Count() > 1).ToList();
                if (duplicates.Any())
                {
                    foreach (var duplicate in duplicates)
                    {
                        result.Errors.Add($"Concurrent UID generation produced duplicate: {duplicate.Key} (count: {duplicate.Count()})");
                    }
                }
                else
                {
                    result.ValidationDetails.Add("Concurrent UID generation test passed: no duplicates found");
                }

                LoggingService.LogDebug($"Concurrent UID generation validation completed: {generatedUIDs.Length} UIDs generated", "DatabaseValidationService");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Concurrent UID generation validation failed: {ex.Message}");
                LoggingService.LogError($"Concurrent UID generation validation error: {ex.Message}", "DatabaseValidationService");
            }
        }

        #endregion

        #region Database Operation Validation

        /// <summary>
        /// Validates database operations with transaction handling and error recovery.
        /// </summary>
        /// <returns>Database operation validation result</returns>
        public async Task<DatabaseOperationValidationResult> ValidateDatabaseOperationsAsync()
        {
            var result = new DatabaseOperationValidationResult();

            try
            {
                LoggingService.LogInfo("Starting database operation validation", "DatabaseValidationService");

                // Validate transaction handling
                await ValidateTransactionHandlingAsync(result);

                // Validate error recovery mechanisms
                await ValidateErrorRecoveryAsync(result);

                // Validate connection management
                await ValidateConnectionManagementAsync(result);

                // Validate data consistency
                await ValidateDataConsistencyAsync(result);

                result.IsValid = !result.Errors.Any();
                result.ValidationTime = DateTime.UtcNow;

                var status = result.IsValid ? "PASSED" : "FAILED";
                LoggingService.LogInfo($"Database operation validation {status}: {result.Errors.Count} errors, {result.Warnings.Count} warnings", "DatabaseValidationService");

                return result;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Database operation validation failed with exception: {ex.Message}");
                result.IsValid = false;
                result.ValidationTime = DateTime.UtcNow;

                ErrorManager.HandleErrorToast(ex, "فشل في التحقق من عمليات قاعدة البيانات", "خطأ في التحقق", LogLevel.Error, "DatabaseValidationService");
                return result;
            }
        }

        /// <summary>
        /// Validates transaction handling mechanisms.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateTransactionHandlingAsync(DatabaseOperationValidationResult result)
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Test transaction rollback
                using var transaction = connection.BeginTransaction();
                
                try
                {
                    // Insert test data
                    await connection.ExecuteAsync(@"
                        INSERT INTO Clients (Uid, NameFr, CreatedAt, UpdatedAt)
                        VALUES ('TEST01', 'Test Client', datetime('now'), datetime('now'))",
                        transaction: transaction);

                    // Verify data exists within transaction
                    var count = await connection.ExecuteScalarAsync<int>(
                        "SELECT COUNT(*) FROM clients WHERE uid = 'TEST01'", 
                        transaction: transaction);

                    if (count != 1)
                    {
                        result.Errors.Add("Transaction isolation failed: data not visible within transaction");
                    }

                    // Rollback transaction
                    transaction.Rollback();

                    // Verify data was rolled back
                    var countAfterRollback = await connection.ExecuteScalarAsync<int>(
                        "SELECT COUNT(*) FROM clients WHERE uid = 'TEST01'");

                    if (countAfterRollback != 0)
                    {
                        result.Errors.Add("Transaction rollback failed: data still exists after rollback");
                    }
                    else
                    {
                        result.ValidationDetails.Add("Transaction rollback test passed");
                    }
                }
                catch (Exception ex)
                {
                    transaction.Rollback();
                    result.Errors.Add($"Transaction handling test failed: {ex.Message}");
                }

                LoggingService.LogDebug("Transaction handling validation completed", "DatabaseValidationService");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Transaction handling validation failed: {ex.Message}");
                LoggingService.LogError($"Transaction handling validation error: {ex.Message}", "DatabaseValidationService");
            }
        }

        /// <summary>
        /// Validates error recovery mechanisms.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateErrorRecoveryAsync(DatabaseOperationValidationResult result)
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Test constraint violation recovery
                try
                {
                    await connection.ExecuteAsync(@"
                        INSERT INTO Clients (Uid, NameFr, CreatedAt, UpdatedAt)
                        VALUES (NULL, 'Test Client', datetime('now'), datetime('now'))");
                    
                    result.Errors.Add("Constraint violation was not caught: NULL UID was allowed");
                }
                catch (SqliteException)
                {
                    result.ValidationDetails.Add("Constraint violation properly caught and handled");
                }

                // Test foreign key constraint
                try
                {
                    await connection.ExecuteAsync(@"
                        INSERT INTO Activities (Uid, ClientUid, CreatedAt, UpdatedAt)
                        VALUES ('INVALID_Act1', 'NONEXISTENT', datetime('now'), datetime('now'))");
                    
                    result.Errors.Add("Foreign key constraint violation was not caught");
                }
                catch (SqliteException)
                {
                    result.ValidationDetails.Add("Foreign key constraint properly enforced");
                }

                LoggingService.LogDebug("Error recovery validation completed", "DatabaseValidationService");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Error recovery validation failed: {ex.Message}");
                LoggingService.LogError($"Error recovery validation error: {ex.Message}", "DatabaseValidationService");
            }
        }

        /// <summary>
        /// Validates connection management.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateConnectionManagementAsync(DatabaseOperationValidationResult result)
        {
            try
            {
                // Test multiple concurrent connections
                var connectionTasks = new List<Task>();
                
                for (int i = 0; i < 10; i++)
                {
                    connectionTasks.Add(Task.Run(async () =>
                    {
                        using var connection = _databaseService.CreateConnection();
                        await connection.OpenAsync();
                        await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM clients");
                    }));
                }

                await Task.WhenAll(connectionTasks);
                result.ValidationDetails.Add("Concurrent connection management test passed");

                LoggingService.LogDebug("Connection management validation completed", "DatabaseValidationService");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Connection management validation failed: {ex.Message}");
                LoggingService.LogError($"Connection management validation error: {ex.Message}", "DatabaseValidationService");
            }
        }

        /// <summary>
        /// Validates data consistency across related tables.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateDataConsistencyAsync(DatabaseOperationValidationResult result)
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Check referential integrity
                var orphanedActivities = await connection.ExecuteScalarAsync<int>(@"
                    SELECT COUNT(*) FROM Activities a
                    LEFT JOIN Clients c ON a.ClientUid = c.Uid
                    WHERE c.Uid IS NULL");

                if (orphanedActivities > 0)
                {
                    result.Errors.Add($"Data consistency violation: {orphanedActivities} orphaned activities found");
                }

                var orphanedPhoneNumbers = await connection.ExecuteScalarAsync<int>(@"
                    SELECT COUNT(*) FROM PhoneNumbers p
                    LEFT JOIN Clients c ON p.ClientUid = c.Uid
                    WHERE c.Uid IS NULL");

                if (orphanedPhoneNumbers > 0)
                {
                    result.Errors.Add($"Data consistency violation: {orphanedPhoneNumbers} orphaned phone numbers found");
                }

                if (orphanedActivities == 0 && orphanedPhoneNumbers == 0)
                {
                    result.ValidationDetails.Add("Data consistency check passed: no orphaned records found");
                }

                LoggingService.LogDebug("Data consistency validation completed", "DatabaseValidationService");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Data consistency validation failed: {ex.Message}");
                LoggingService.LogError($"Data consistency validation error: {ex.Message}", "DatabaseValidationService");
            }
        }

        #endregion

        #region Business Rule Validation

        /// <summary>
        /// Validates business rules for all activity types and file check rules.
        /// </summary>
        /// <returns>Business rule validation result</returns>
        public async Task<BusinessRuleValidationResult> ValidateBusinessRulesAsync()
        {
            var result = new BusinessRuleValidationResult();

            try
            {
                LoggingService.LogInfo("Starting business rule validation", "DatabaseValidationService");

                // Validate activity type rules
                await ValidateActivityTypeRulesAsync(result);

                // Validate craft activity rules
                await ValidateCraftActivityRulesAsync(result);

                // Validate file check rules
                await ValidateFileCheckRulesAsync(result);

                // Validate phone number rules
                await ValidatePhoneNumberRulesAsync(result);

                // Validate payment year rules
                await ValidatePaymentYearRulesAsync(result);

                result.IsValid = !result.Errors.Any();
                result.ValidationTime = DateTime.UtcNow;

                var status = result.IsValid ? "PASSED" : "FAILED";
                LoggingService.LogInfo($"Business rule validation {status}: {result.Errors.Count} errors, {result.Warnings.Count} warnings", "DatabaseValidationService");

                return result;
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Business rule validation failed with exception: {ex.Message}");
                result.IsValid = false;
                result.ValidationTime = DateTime.UtcNow;

                ErrorManager.HandleErrorToast(ex, "فشل في التحقق من قواعد العمل", "خطأ في التحقق", LogLevel.Error, "DatabaseValidationService");
                return result;
            }
        }

        /// <summary>
        /// Validates activity type business rules.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateActivityTypeRulesAsync(BusinessRuleValidationResult result)
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Get total activities for validation count
                result.ActivitiesValidated = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM Activities");

                // Validate activity types are within allowed values
                var invalidActivityTypes = await connection.QueryAsync<string>(@"
                    SELECT DISTINCT ActivityType FROM Activities
                    WHERE ActivityType NOT IN (@MainCommercial, @SecondaryCommercial, @Craft, @Professional)
                    AND ActivityType IS NOT NULL",
                    new {
                        MainCommercial = ActivityTypeMainCommercial,
                        SecondaryCommercial = ActivityTypeSecondaryCommercial,
                        Craft = ActivityTypeCraft,
                        Professional = ActivityTypeProfessional
                    });

                foreach (var activityType in invalidActivityTypes)
                {
                    result.Errors.Add($"Invalid activity type found: {activityType}");
                }

                // Validate commercial activities have activity codes
                var commercialWithoutCodes = await connection.ExecuteScalarAsync<int>(@"
                    SELECT COUNT(*) FROM Activities a
                    LEFT JOIN CommercialActivityCodes cac ON a.Uid = cac.ActivityUid
                    WHERE a.ActivityType IN (@MainCommercial, @SecondaryCommercial)
                    AND cac.ActivityUid IS NULL",
                    new {
                        MainCommercial = ActivityTypeMainCommercial,
                        SecondaryCommercial = ActivityTypeSecondaryCommercial
                    });

                if (commercialWithoutCodes > 0)
                {
                    result.Warnings.Add($"Found {commercialWithoutCodes} commercial activities without activity codes");
                }

                // Validate non-commercial activities have descriptions
                var nonCommercialWithoutDescriptions = await connection.ExecuteScalarAsync<int>(@"
                    SELECT COUNT(*) FROM Activities a
                    LEFT JOIN ProfessionNames pn ON a.Uid = pn.ActivityUid
                    WHERE a.ActivityType IN (@Craft, @Professional)
                    AND pn.ActivityUid IS NULL",
                    new {
                        Craft = ActivityTypeCraft,
                        Professional = ActivityTypeProfessional
                    });

                if (nonCommercialWithoutDescriptions > 0)
                {
                    result.Warnings.Add($"Found {nonCommercialWithoutDescriptions} non-commercial activities without descriptions");
                }

                LoggingService.LogDebug($"Activity type rules validation completed: {result.ActivitiesValidated} activities validated", "DatabaseValidationService");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Activity type rules validation failed: {ex.Message}");
                LoggingService.LogError($"Activity type rules validation error: {ex.Message}", "DatabaseValidationService");
            }
        }

        /// <summary>
        /// Validates craft activity business rules including one-to-one constraints.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateCraftActivityRulesAsync(BusinessRuleValidationResult result)
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Validate one-to-one constraint: each activity can have only one craft code
                var activitiesWithMultipleCraftCodes = await connection.ExecuteScalarAsync<int>(@"
                    SELECT COUNT(*)
                    FROM CraftActivityCodes
                    GROUP BY ActivityUid
                    HAVING COUNT(*) > 1");

                if (activitiesWithMultipleCraftCodes > 0)
                {
                    result.Errors.Add($"One-to-one constraint violation: {activitiesWithMultipleCraftCodes} activities have multiple craft codes");
                }

                // Validate craft code format (XX-XX-XXX)
                var invalidCraftCodes = await connection.ExecuteScalarAsync<int>(@"
                    SELECT COUNT(*)
                    FROM CraftActivityCodes
                    WHERE CraftCode NOT GLOB '[0-9][0-9]-[0-9][0-9]-[0-9][0-9][0-9]'");

                if (invalidCraftCodes > 0)
                {
                    result.Errors.Add($"Craft code format violation: {invalidCraftCodes} craft codes do not match XX-XX-XXX format");
                }

                // Validate craft codes exist in CraftTypeBase using reference database service
                // Cross-database JOINs are not supported; check existence via CraftTypeBaseService
                try
                {
                    var craftService = ServiceLocator.GetService<CraftTypeBaseService>();
                    int missingCount = 0;

                    // Fetch distinct craft codes from client DB and check each against reference DB
                    var distinctCodes = await connection.QueryAsync<string>("SELECT DISTINCT CraftCode FROM CraftActivityCodes");
                    foreach (var code in distinctCodes)
                    {
                        var craft = await craftService.GetByCodeAsync(code);
                        if (craft == null)
                        {
                            missingCount++;
                        }
                    }

                    if (missingCount > 0)
                    {
                        result.Warnings.Add($"Found {missingCount} craft codes that do not exist in CraftTypeBase");
                    }
                }
                catch (Exception ex)
                {
                    LoggingService.LogWarning($"CraftTypeBase existence validation via service failed: {ex.Message}", "DatabaseValidationService");
                }

                // Validate craft activities have corresponding Activities records
                var orphanedCraftActivities = await connection.ExecuteScalarAsync<int>(@"
                    SELECT COUNT(*)
                    FROM CraftActivityCodes cac
                    LEFT JOIN Activities a ON cac.ActivityUid = a.Uid
                    WHERE a.Uid IS NULL");

                if (orphanedCraftActivities > 0)
                {
                    result.Errors.Add($"Data integrity violation: {orphanedCraftActivities} craft activity codes have no corresponding Activities record");
                }

                // This validation is no longer needed since ClientUid was removed from CraftActivityCodes
                // The relationship is now maintained through Activities table only

                // Count total craft activities validated
                var totalCraftActivities = await connection.ExecuteScalarAsync<int>(@"
                    SELECT COUNT(*) FROM CraftActivityCodes");

                result.ValidationDetails.Add($"Validated {totalCraftActivities} craft activities");

                LoggingService.LogDebug($"Craft activity rules validation completed: {totalCraftActivities} craft activities validated", "DatabaseValidationService");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Craft activity rules validation failed: {ex.Message}");
                LoggingService.LogError($"Craft activity rules validation error: {ex.Message}", "DatabaseValidationService");
            }
        }

        /// <summary>
        /// Validates file check business rules based on activity types.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task ValidateFileCheckRulesAsync(BusinessRuleValidationResult result)
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                var activitiesWithFileChecks = await GetActivitiesWithFileChecksAsync(connection);
                
                foreach (var activity in activitiesWithFileChecks)
                {
                    ValidateActivityFileChecks(activity, result);
                }

                result.FileCheckRulesValidated = activitiesWithFileChecks.Count();
                LoggingService.LogDebug($"File check rules validation completed: {result.FileCheckRulesValidated} activities validated", "DatabaseValidationService");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"File check rules validation failed: {ex.Message}");
                LoggingService.LogError($"File check rules validation error: {ex.Message}", "DatabaseValidationService");
            }
        }

        /// <summary>
        /// Gets activities with their file check states from the database.
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <returns>Activities with file check data</returns>
        private async Task<IEnumerable<dynamic>> GetActivitiesWithFileChecksAsync(SqliteConnection connection)
        {
            return await connection.QueryAsync<dynamic>(@"
                SELECT a.Uid, a.ActivityType,
                       GROUP_CONCAT(fcs.FileCheckType) as file_check_types
                FROM Activities a
                LEFT JOIN FileCheckStates fcs ON a.Uid = fcs.ActivityUid
                WHERE a.ActivityType IS NOT NULL
                GROUP BY a.Uid, a.ActivityType");
        }

        /// <summary>
        /// Validates file checks for a single activity.
        /// </summary>
        /// <param name="activity">Activity data</param>
        /// <param name="result">Validation result to update</param>
        private void ValidateActivityFileChecks(dynamic activity, BusinessRuleValidationResult result)
        {
            var fileCheckTypes = activity.file_check_types?.ToString()?.Split(',') ?? new string[0];
            var activityType = activity.ActivityType?.ToString();
            var activityUID = activity.Uid?.ToString();

            switch (activityType)
            {
                case ActivityTypeMainCommercial:
                case ActivityTypeSecondaryCommercial:
                    ValidateCommercialFileChecks(activityUID, fileCheckTypes, result);
                    break;
                case ActivityTypeCraft:
                    ValidateCraftFileChecks(activityUID, fileCheckTypes, result);
                    break;
                case ActivityTypeProfessional:
                    ValidateProfessionalFileChecks(activityUID, fileCheckTypes, result);
                    break;
                default:
                    result.Warnings.Add($"Unknown activity type for validation: {activityType} (Activity: {activityUID})");
                    break;
            }
        }

        /// <summary>
        /// Validates commercial activity file check requirements.
        /// </summary>
        /// <param name="activityUID">Activity UID</param>
        /// <param name="fileCheckTypes">File check types</param>
        /// <param name="result">Validation result</param>
        private void ValidateCommercialFileChecks(string activityUID, string[] fileCheckTypes, BusinessRuleValidationResult result)
        {
            var requiredTypes = new[] { FileCheckTypeCAS, FileCheckTypeNIF, FileCheckTypeNIS, FileCheckTypeRC, FileCheckTypeDEX };
            ValidateFileCheckTypes(activityUID, "Commercial", fileCheckTypes, requiredTypes, result);
        }

        /// <summary>
        /// Validates craft activity file check requirements.
        /// </summary>
        /// <param name="activityUID">Activity UID</param>
        /// <param name="fileCheckTypes">File check types</param>
        /// <param name="result">Validation result</param>
        private void ValidateCraftFileChecks(string activityUID, string[] fileCheckTypes, BusinessRuleValidationResult result)
        {
            var requiredTypes = new[] { FileCheckTypeCAS, FileCheckTypeNIF, FileCheckTypeNIS, FileCheckTypeART, FileCheckTypeDEX };
            ValidateFileCheckTypes(activityUID, "Craft", fileCheckTypes, requiredTypes, result);
        }

        /// <summary>
        /// Validates professional activity file check requirements.
        /// </summary>
        /// <param name="activityUID">Activity UID</param>
        /// <param name="fileCheckTypes">File check types</param>
        /// <param name="result">Validation result</param>
        private void ValidateProfessionalFileChecks(string activityUID, string[] fileCheckTypes, BusinessRuleValidationResult result)
        {
            var requiredTypes = new[] { FileCheckTypeCAS, FileCheckTypeNIF, FileCheckTypeNIS, FileCheckTypeAGR, FileCheckTypeDEX };
            ValidateFileCheckTypes(activityUID, "Professional", fileCheckTypes, requiredTypes, result);
        }

        /// <summary>
        /// Common validation logic for file check types.
        /// </summary>
        /// <param name="activityUID">Activity UID</param>
        /// <param name="activityTypeName">Activity type name for messages</param>
        /// <param name="actualTypes">Actual file check types</param>
        /// <param name="requiredTypes">Required file check types</param>
        /// <param name="result">Validation result</param>
        private void ValidateFileCheckTypes(string activityUID, string activityTypeName, string[] actualTypes, string[] requiredTypes, BusinessRuleValidationResult result)
        {
            // Filter out empty strings from actualTypes
            var cleanActualTypes = actualTypes.Where(t => !string.IsNullOrWhiteSpace(t)).ToArray();
            
            var missingTypes = requiredTypes.Except(cleanActualTypes).ToList();
            if (missingTypes.Any())
            {
                result.Warnings.Add($"{activityTypeName} activity {activityUID} missing file check types: {string.Join(", ", missingTypes)}");
            }

            // Check for invalid types
            var invalidTypes = cleanActualTypes.Except(requiredTypes).ToList();
            if (invalidTypes.Any())
            {
                result.Errors.Add($"{activityTypeName} activity {activityUID} has invalid file check types: {string.Join(", ", invalidTypes)}");
            }
        }

        /// <summary>
        /// Validates phone number business rules.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task ValidatePhoneNumberRulesAsync(BusinessRuleValidationResult result)
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                // Get total phone numbers for validation count
                result.PhoneNumberRulesValidated = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM PhoneNumbers");

                // Check for clients with multiple primary phone numbers
                var clientsWithMultiplePrimary = await connection.QueryAsync<dynamic>(@"
                    SELECT ClientUid, COUNT(*) as primary_count
                    FROM PhoneNumbers
                    WHERE IsPrimary = 1
                    GROUP BY ClientUid
                    HAVING COUNT(*) > 1");

                foreach (var client in clientsWithMultiplePrimary)
                {
                    result.Errors.Add($"Client {client.ClientUid} has multiple primary phone numbers ({client.primary_count})");
                }

                // Check for invalid phone number formats
                var invalidPhoneNumbers = await connection.ExecuteScalarAsync<int>(@"
                    SELECT COUNT(*) FROM PhoneNumbers
                    WHERE PhoneNumber IS NOT NULL
                    AND LENGTH(TRIM(PhoneNumber)) < @MinLength",
                    new { MinLength = MinPhoneNumberLength });

                if (invalidPhoneNumbers > 0)
                {
                    result.Warnings.Add($"Found {invalidPhoneNumbers} potentially invalid phone numbers (too short)");
                }

                LoggingService.LogDebug($"Phone number rules validation completed: {result.PhoneNumberRulesValidated} phone numbers validated", "DatabaseValidationService");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Phone number rules validation failed: {ex.Message}");
                LoggingService.LogError($"Phone number rules validation error: {ex.Message}", "DatabaseValidationService");
            }
        }

        /// <summary>
        /// Validates payment year business rules.
        /// </summary>
        /// <param name="result">Validation result to update</param>
        private async Task ValidatePaymentYearRulesAsync(BusinessRuleValidationResult result)
        {
            try
            {
                using var connection = _databaseService.CreateConnection();
                await connection.OpenAsync();

                var currentYear = DateTime.Now.Year;

                // Get total payment year records for validation count
                var g12Count = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM g12_check");
                var bisCount = await connection.ExecuteScalarAsync<int>("SELECT COUNT(*) FROM bis_check");
                result.PaymentYearRulesValidated = g12Count + bisCount;

                // Check for invalid G12 payment years
                var invalidG12Years = await connection.ExecuteScalarAsync<int>(@"
                    SELECT COUNT(*) FROM g12_check 
                    WHERE payment_year < @MinYear OR payment_year > @MaxYear", 
                    new { MinYear = MinValidPaymentYear, MaxYear = currentYear + 1 });

                if (invalidG12Years > 0)
                {
                    result.Warnings.Add($"Found {invalidG12Years} G12 records with invalid payment years");
                }

                // Check for invalid BIS payment years
                var invalidBisYears = await connection.ExecuteScalarAsync<int>(@"
                    SELECT COUNT(*) FROM bis_check 
                    WHERE payment_year < @MinYear OR payment_year > @MaxYear", 
                    new { MinYear = MinValidPaymentYear, MaxYear = currentYear + 1 });

                if (invalidBisYears > 0)
                {
                    result.Warnings.Add($"Found {invalidBisYears} BIS records with invalid payment years");
                }

                LoggingService.LogDebug($"Payment year rules validation completed: {result.PaymentYearRulesValidated} payment year records validated", "DatabaseValidationService");
            }
            catch (Exception ex)
            {
                result.Errors.Add($"Payment year rules validation failed: {ex.Message}");
                LoggingService.LogError($"Payment year rules validation error: {ex.Message}", "DatabaseValidationService");
            }
        }

        #endregion

        #region Comprehensive Validation

        /// <summary>
        /// Performs comprehensive validation of all database aspects.
        /// </summary>
        /// <returns>Comprehensive validation result</returns>
        public async Task<ComprehensiveValidationResult> PerformComprehensiveValidationAsync()
        {
            var result = new ComprehensiveValidationResult
            {
                StartTime = DateTime.UtcNow
            };

            try
            {
                LoggingService.LogInfo("Starting comprehensive database validation", "DatabaseValidationService");

                // Schema validation
                var schemaValidationResult = await _schemaValidator.ValidateSchemaAsync();
                result.SchemaValidation = schemaValidationResult;

                // UID validation
                result.UIDValidation = await ValidateUIDGenerationAsync();

                // Database operation validation
                result.DatabaseOperationValidation = await ValidateDatabaseOperationsAsync();

                // Business rule validation
                result.BusinessRuleValidation = await ValidateBusinessRulesAsync();

                // Overall result
                result.IsValid = result.SchemaValidation.IsValid &&
                                result.UIDValidation.IsValid &&
                                result.DatabaseOperationValidation.IsValid &&
                                result.BusinessRuleValidation.IsValid;

                result.EndTime = DateTime.UtcNow;
                result.TotalDurationMs = (long)(result.EndTime - result.StartTime).TotalMilliseconds;

                var status = result.IsValid ? "PASSED" : "FAILED";
                LoggingService.LogInfo($"Comprehensive validation {status} in {result.TotalDurationMs}ms", "DatabaseValidationService");

                return result;
            }
            catch (Exception ex)
            {
                result.IsValid = false;
                result.EndTime = DateTime.UtcNow;
                result.TotalDurationMs = (long)(result.EndTime - result.StartTime).TotalMilliseconds;
                result.ErrorMessage = ex.Message;

                ErrorManager.HandleErrorToast(ex, "فشل في التحقق الشامل من قاعدة البيانات", "خطأ في التحقق", LogLevel.Error, "DatabaseValidationService");
                return result;
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of the DatabaseValidationService resources.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Protected dispose method.
        /// </summary>
        /// <param name="disposing">True if disposing managed resources</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources if any
                    LoggingService.LogDebug("DatabaseValidationService disposed", "DatabaseValidationService");
                }
                _disposed = true;
            }
        }

        #endregion
    }
}
