=== UFU2 Application Session Started at 2025-08-09 14:26:43 ===
[2025-08-09 14:26:43.726]  	[INFO]		[LoggingService]	Log level set to Debug
[2025-08-09 14:26:43.734]  	[INFO]		[App]	UFU2 Application starting up
[2025-08-09 14:26:43.791]  	[DEBUG]		[ServiceLocator]	Initializing ServiceLocator
[2025-08-09 14:26:43.799]  	[DEBUG]		[ServiceLocator]	Service IToastService registered
[2025-08-09 14:26:43.892]  	[DEBUG]		[ServiceLocator]	Service ValidationService registered
[2025-08-09 14:26:43.898]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ValidationService
[2025-08-09 14:26:43.909]  	[DEBUG]		[WindowChromeService]	Initializing WindowChromeService
[2025-08-09 14:26:43.917]  	[DEBUG]		[WindowChromeService]	Updating window chrome theme to: Dark
[2025-08-09 14:26:43.924]  	[WARN]		[ThemeManager]	Theme color 'ButtonHoverBackground' not found in current theme
[2025-08-09 14:26:43.929]  	[WARN]		[ThemeManager]	Theme color 'ButtonPressedBackground' not found in current theme
[2025-08-09 14:26:43.936]  	[DEBUG]		[WindowChromeService]	Current theme updated for Dark
[2025-08-09 14:26:43.942]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarBackground: #E61D1F20
[2025-08-09 14:26:43.947]  	[DEBUG]		[WindowChromeService]	Theme updated - TitleBarForeground: #FFFFFFFF
[2025-08-09 14:26:43.952]  	[DEBUG]		[WindowChromeService]	Theme updated - WindowBorderBrush: #E6353A3E
[2025-08-09 14:26:43.965]  	[DEBUG]		[WindowChromeService]	Window chrome theme updated successfully to Dark
[2025-08-09 14:26:43.972]  	[DEBUG]		[WindowChromeService]	Subscribed to ThemeManager events
[2025-08-09 14:26:43.978]  	[DEBUG]		[WindowChromeService]	WindowChromeService initialized successfully
[2025-08-09 14:26:43.984]  	[DEBUG]		[ServiceLocator]	Service IWindowChromeService registered
[2025-08-09 14:26:43.998]  	[DEBUG]		[DispatcherOptimizationService]	DispatcherOptimizationService initialized with smart batching and UI thread monitoring
[2025-08-09 14:26:44.004]  	[DEBUG]		[ServiceLocator]	Service DispatcherOptimizationService registered
[2025-08-09 14:26:44.010]  	[DEBUG]		[UIResponsivenessMonitoringService]	UI responsiveness monitoring started
[2025-08-09 14:26:44.014]  	[DEBUG]		[UIResponsivenessMonitoringService]	UIResponsivenessMonitoringService initialized
[2025-08-09 14:26:44.019]  	[DEBUG]		[ServiceLocator]	Service UIResponsivenessMonitoringService registered
[2025-08-09 14:26:44.032]  	[INFO]		[BackgroundViewInitializationService]	BackgroundViewInitializationService initialized
[2025-08-09 14:26:44.040]  	[DEBUG]		[ServiceLocator]	Service BackgroundViewInitializationService registered
[2025-08-09 14:26:44.047]  	[INFO]		[ViewMemoryOptimizationService]	ViewMemoryOptimizationService initialized
[2025-08-09 14:26:44.051]  	[DEBUG]		[ServiceLocator]	Service ViewMemoryOptimizationService registered
[2025-08-09 14:26:44.059]  	[INFO]		[ViewLoadingMonitoringService]	ViewLoadingMonitoringService initialized
[2025-08-09 14:26:44.069]  	[DEBUG]		[ServiceLocator]	Service ViewLoadingMonitoringService registered
[2025-08-09 14:26:44.084]  	[INFO]		[PerformanceDashboardService]	PerformanceDashboardService initialized
[2025-08-09 14:26:44.092]  	[DEBUG]		[ServiceLocator]	Service PerformanceDashboardService registered
[2025-08-09 14:26:44.099]  	[INFO]		[ServiceLocator]	Initializing Phase 2D memory management services
[2025-08-09 14:26:44.106]  	[INFO]		[ResourceManager]	ResourceManager initialized with automatic cleanup and memory monitoring
[2025-08-09 14:26:44.112]  	[DEBUG]		[ServiceLocator]	Service ResourceManager registered
[2025-08-09 14:26:44.117]  	[DEBUG]		[ServiceLocator]	ResourceManager registered successfully
[2025-08-09 14:26:44.126]  	[INFO]		[WeakEventManager]	WeakEventManager initialized with automatic cleanup
[2025-08-09 14:26:44.143]  	[DEBUG]		[ServiceLocator]	Service WeakEventManager registered
[2025-08-09 14:26:44.149]  	[DEBUG]		[ServiceLocator]	WeakEventManager registered successfully
[2025-08-09 14:26:44.168]  	[DEBUG]		[MemoryLeakDetectionService]	Memory snapshot taken: 116.80MB working set
[2025-08-09 14:26:44.176]  	[INFO]		[MemoryLeakDetectionService]	MemoryLeakDetectionService initialized with automatic monitoring
[2025-08-09 14:26:44.181]  	[DEBUG]		[ServiceLocator]	Service MemoryLeakDetectionService registered
[2025-08-09 14:26:44.186]  	[DEBUG]		[ServiceLocator]	MemoryLeakDetectionService registered successfully
[2025-08-09 14:26:44.193]  	[INFO]		[ServiceLocator]	Phase 2D memory management services initialized successfully
[2025-08-09 14:26:44.199]  	[DEBUG]		[ServiceLocator]	ServiceLocator initialized successfully with comprehensive UI optimization, background processing, performance dashboard, and memory management services
[2025-08-09 14:26:44.212]  	[DEBUG]		[ThemeManager]	Initializing ThemeManager
[2025-08-09 14:26:44.241]  	[DEBUG]		[ThemeManager]	Theme resource dictionaries loaded successfully
[2025-08-09 14:26:44.248]  	[DEBUG]		[ThemeManager]	Found MaterialDesign BundledTheme in application resources
[2025-08-09 14:26:44.258]  	[DEBUG]		[ThemeManager]	ThemeManager initialized with Dark theme
[2025-08-09 14:26:44.589]  	[DEBUG]		[MainWindow]	Initializing custom window chrome for MainWindow
[2025-08-09 14:26:44.596]  	[DEBUG]		[MainWindow]	Getting WindowChromeService from ServiceLocator
[2025-08-09 14:26:44.601]  	[DEBUG]		[MainWindow]	WindowChromeService retrieved successfully
[2025-08-09 14:26:44.613]  	[DEBUG]		[MainWindow]	Creating CustomWindowChromeViewModel
[2025-08-09 14:26:44.626]  	[DEBUG]		[ResourceManager]	Registered resource: CustomWindowChromeViewModel_51489795_638903428046237916 (BaseViewModel) for CustomWindowChromeViewModel
[2025-08-09 14:26:44.631]  	[DEBUG]		[CustomWindowChromeViewModel]	BaseViewModel memory management initialized for CustomWindowChromeViewModel
[2025-08-09 14:26:44.637]  	[DEBUG]		[CustomWindowChromeViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:26:44.642]  	[DEBUG]		[CustomWindowChromeViewModel]	Initializing CustomWindowChromeViewModel
[2025-08-09 14:26:44.660]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU2
[2025-08-09 14:26:44.667]  	[DEBUG]		[CustomWindowChromeViewModel]	Properties initialized with default values
[2025-08-09 14:26:44.674]  	[DEBUG]		[CustomWindowChromeViewModel]	Window control commands initialized successfully
[2025-08-09 14:26:44.679]  	[DEBUG]		[CustomWindowChromeViewModel]	Subscribed to theme change events
[2025-08-09 14:26:44.685]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarBackground' not found or invalid type
[2025-08-09 14:26:44.695]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_TitleBarForeground' not found or invalid type
[2025-08-09 14:26:44.700]  	[WARN]		[CustomWindowChromeViewModel]	Brush resource 'DarkTheme_WindowBorder' not found or invalid type
[2025-08-09 14:26:44.705]  	[DEBUG]		[CustomWindowChromeViewModel]	Theme properties updated for Dark theme
[2025-08-09 14:26:44.713]  	[DEBUG]		[CustomWindowChromeViewModel]	Applied current theme: Dark
[2025-08-09 14:26:44.725]  	[DEBUG]		[CustomWindowChromeViewModel]	Updating RTL layout for culture: en-US, IsRTL: False
[2025-08-09 14:26:44.731]  	[DEBUG]		[CustomWindowChromeViewModel]	RTL layout updated - FlowDirection: LeftToRight, TitleAlignment: Left, ControlsAlignment: Right
[2025-08-09 14:26:44.736]  	[DEBUG]		[CustomWindowChromeViewModel]	CustomWindowChromeViewModel initialized successfully
[2025-08-09 14:26:44.741]  	[DEBUG]		[MainWindow]	CustomWindowChromeViewModel created successfully
[2025-08-09 14:26:44.746]  	[DEBUG]		[MainWindow]	Setting window properties
[2025-08-09 14:26:44.750]  	[DEBUG]		[CustomWindowChromeViewModel]	Window title changed to: UFU Client Management
[2025-08-09 14:26:44.756]  	[DEBUG]		[CustomWindowChromeViewModel]	Window icon changed
[2025-08-09 14:26:44.762]  	[DEBUG]		[CustomWindowChromeViewModel]	Command states refreshed
[2025-08-09 14:26:44.767]  	[DEBUG]		[CustomWindowChromeViewModel]	Window state updated to: Normal
[2025-08-09 14:26:44.772]  	[DEBUG]		[MainWindow]	Window properties set successfully
[2025-08-09 14:26:44.778]  	[DEBUG]		[MainWindow]	Setting DataContext
[2025-08-09 14:26:44.783]  	[DEBUG]		[MainWindow]	DataContext set successfully
[2025-08-09 14:26:44.788]  	[DEBUG]		[MainWindow]	Applying WindowChrome
[2025-08-09 14:26:44.794]  	[DEBUG]		[WindowChromeService]	Configuring window chrome for MainWindow with ViewModel CustomWindowChromeViewModel
[2025-08-09 14:26:44.800]  	[DEBUG]		[WindowChromeService]	Applying custom chrome to window: MainWindow (Title: UFU Client Management)
[2025-08-09 14:26:44.805]  	[DEBUG]		[WindowChromeService]	Validating window for chrome application: MainWindow
[2025-08-09 14:26:44.811]  	[WARN]		[WindowChromeService]	Window is not loaded, cannot apply chrome
[2025-08-09 14:26:44.816]  	[WARN]		[WindowChromeService]	Window validation failed for chrome application: MainWindow
[2025-08-09 14:26:44.822]  	[DEBUG]		[WindowChromeService]	Applying fallback chrome to window: MainWindow
[2025-08-09 14:26:44.827]  	[DEBUG]		[WindowChromeService]	Existing WindowChrome removed
[2025-08-09 14:26:44.832]  	[DEBUG]		[WindowChromeService]	Creating fallback WindowChrome with safe defaults
[2025-08-09 14:26:44.840]  	[DEBUG]		[WindowChromeService]	Fallback WindowChrome created successfully
[2025-08-09 14:26:44.845]  	[DEBUG]		[WindowChromeService]	Fallback chrome applied successfully
[2025-08-09 14:26:44.851]  	[DEBUG]		[WindowChromeService]	Custom chrome applied during configuration
[2025-08-09 14:26:44.904]  	[DEBUG]		[WindowChromeService]	Ensuring native window behaviors for: UFU Client Management
[2025-08-09 14:26:44.917]  	[DEBUG]		[WindowChromeService]	Ensuring optimal WindowChrome configuration for native behaviors
[2025-08-09 14:26:44.944]  	[DEBUG]		[WindowChromeService]	WindowChrome configuration optimized for native behaviors
[2025-08-09 14:26:44.957]  	[DEBUG]		[WindowChromeService]	Validating Aero Snap functionality for window: UFU Client Management
[2025-08-09 14:26:44.965]  	[DEBUG]		[WindowChromeService]	Aero Snap functionality validation completed successfully
[2025-08-09 14:26:44.975]  	[DEBUG]		[WindowChromeService]	Validating window resizing functionality for: UFU Client Management
[2025-08-09 14:26:44.982]  	[DEBUG]		[WindowChromeService]	Resize border thickness is adequate: 8,8,8,8
[2025-08-09 14:26:44.986]  	[DEBUG]		[WindowChromeService]	Window resizing validation completed successfully
[2025-08-09 14:26:44.992]  	[DEBUG]		[WindowChromeService]	Native window behaviors configuration completed
[2025-08-09 14:26:44.997]  	[DEBUG]		[WindowChromeService]	Native window behaviors ensured
[2025-08-09 14:26:45.003]  	[DEBUG]		[WindowChromeService]	Validating window configuration
[2025-08-09 14:26:45.010]  	[DEBUG]		[WindowChromeService]	Window configuration validation passed
[2025-08-09 14:26:45.014]  	[DEBUG]		[WindowChromeService]	Window chrome configuration completed successfully
[2025-08-09 14:26:45.019]  	[DEBUG]		[MainWindow]	WindowChrome applied successfully
[2025-08-09 14:26:45.025]  	[DEBUG]		[MainWindow]	Subscribed to window StateChanged event
[2025-08-09 14:26:45.031]  	[DEBUG]		[MainWindow]	Custom window chrome initialized successfully
[2025-08-09 14:26:45.037]  	[DEBUG]		[MainWindow]	Initializing keyboard support and accessibility features
[2025-08-09 14:26:45.042]  	[DEBUG]		[MainWindow]	Keyboard support and accessibility features initialized successfully
[2025-08-09 14:26:45.048]  	[DEBUG]		[ToastService]	Subscribed to theme change events
[2025-08-09 14:26:45.055]  	[DEBUG]		[ToastService]	ToastService initialized in desktop-only mode with position: BottomRight
[2025-08-09 14:26:45.062]  	[DEBUG]		[MainWindow]	ToastService initialized successfully in desktop-only mode
[2025-08-09 14:26:45.072]  	[WARN]		[ThemeManager]	Color 'PrimaryColor' not found in theme dictionary
[2025-08-09 14:26:45.076]  	[WARN]		[ThemeManager]	Color 'SecondaryColor' not found in theme dictionary
[2025-08-09 14:26:45.081]  	[DEBUG]		[ThemeManager]	Custom MaterialDesign colors applied for Dark theme
[2025-08-09 14:26:45.087]  	[DEBUG]		[ThemeManager]	MaterialDesign theme updated to Dark with UFU2 custom colors
[2025-08-09 14:26:45.097]  	[DEBUG]		[ThemeManager]	Removed 1 existing theme dictionaries
[2025-08-09 14:26:45.103]  	[DEBUG]		[ThemeManager]	Custom theme resources updated to Dark
[2025-08-09 14:26:45.110]  	[DEBUG]		[ThemeManager]	Theme application completed for Dark
[2025-08-09 14:26:45.117]  	[DEBUG]		[CustomWindowChromeViewModel]	Processed 2 Normal priority property notifications
[2025-08-09 14:26:45.123]  	[DEBUG]		[ThemeManager]	ThemeManager.Initialization completed in 912ms
[2025-08-09 14:26:45.128]  	[DEBUG]		[App]	ThemeManager initialized successfully
[2025-08-09 14:26:45.136]  	[DEBUG]		[ServiceLocator]	Initializing three-database architecture services
[2025-08-09 14:26:45.167]  	[INFO]		[DatabaseService]	DatabaseService initialized for ClientData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\UFU2_Database.db and connection pool (max: 10)
[2025-08-09 14:26:45.175]  	[DEBUG]		[ServiceLocator]	Service DatabaseService registered
[2025-08-09 14:26:45.184]  	[INFO]		[DatabaseService]	DatabaseService initialized for ReferenceData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\APP_Database.db and connection pool (max: 10)
[2025-08-09 14:26:45.192]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceDatabase' registered with type DatabaseService
[2025-08-09 14:26:45.197]  	[INFO]		[DatabaseService]	DatabaseService initialized for ArchiveData with path: C:\Users\<USER>\AppData\Roaming\UFU2\Data\Archive_Database.db and connection pool (max: 10)
[2025-08-09 14:26:45.203]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveDatabase' registered with type DatabaseService
[2025-08-09 14:26:45.208]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 14:26:45.215]  	[DEBUG]		[ServiceLocator]	Service DatabaseMigrationService registered
[2025-08-09 14:26:45.223]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 14:26:45.241]  	[DEBUG]		[ServiceLocator]	Named service 'ReferenceMigrationService' registered with type DatabaseMigrationService
[2025-08-09 14:26:45.256]  	[DEBUG]		[DatabaseMigrationService]	DatabaseMigrationService initialized
[2025-08-09 14:26:45.268]  	[DEBUG]		[ServiceLocator]	Named service 'ArchiveMigrationService' registered with type DatabaseMigrationService
[2025-08-09 14:26:45.277]  	[INFO]		[ServiceLocator]	Initializing client database schema
[2025-08-09 14:26:45.284]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 14:26:45.324]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:26:45.324]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:26:45.324]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:26:45.333]  	[DEBUG]		[DatabaseService]	Created new pooled database connection
[2025-08-09 14:26:45.337]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:26:45.342]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:26:45.347]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 14:26:45.349]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 14:26:45.353]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 14:26:45.324]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:26:45.362]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 14:26:45.378]  	[DEBUG]		[DatabaseService]	Connection PRAGMA configuration completed
[2025-08-09 14:26:45.381]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 14:26:45.385]  	[INFO]		[DatabaseService]	Initialized connection pool with 2 connections
[2025-08-09 14:26:45.391]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 14:26:45.399]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 14:26:45.406]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 14:26:45.411]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 14:26:45.416]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 14:26:45.440]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 14:26:45.464]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 14:26:45.474]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 14:26:45.480]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:26:45.486]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 14:26:45.524]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 14:26:45.532]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 14:26:45.542]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 14:26:45.551]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 14:26:45.558]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 14:26:45.564]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 14:26:45.570]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 14:26:45.584]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 14:26:45.594]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 14:26:45.600]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 14:26:45.611]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 14:26:45.617]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 14:26:45.626]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 14:26:45.632]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 14:26:45.640]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 14:26:45.648]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 14:26:45.657]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 14:26:45.665]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 14:26:45.672]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 14:26:45.680]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 14:26:45.689]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 14:26:45.696]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 14:26:45.704]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 14:26:45.711]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 14:26:45.720]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 14:26:45.730]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 14:26:45.743]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 14:26:45.752]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 14:26:45.760]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:45.766]  	[INFO]		[ServiceLocator]	Initializing reference database schema
[2025-08-09 14:26:45.772]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 14:26:45.778]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:45.784]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 14:26:45.791]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 14:26:45.797]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 14:26:45.804]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 14:26:45.812]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 14:26:45.820]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 14:26:45.826]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 14:26:45.832]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 14:26:45.838]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 14:26:45.843]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 14:26:45.848]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 14:26:45.854]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:26:45.862]  	[DEBUG]		[DatabaseMigrationService]	Validating 5 required tables
[2025-08-09 14:26:45.868]  	[DEBUG]		[DatabaseMigrationService]	Table 'ActivityTypeBase' exists
[2025-08-09 14:26:45.874]  	[DEBUG]		[DatabaseMigrationService]	Table 'CraftTypeBase' exists
[2025-08-09 14:26:45.880]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiWilayas' exists
[2025-08-09 14:26:45.885]  	[DEBUG]		[DatabaseMigrationService]	Table 'CpiDairas' exists
[2025-08-09 14:26:45.891]  	[DEBUG]		[DatabaseMigrationService]	Table 'ReferenceSchemaVersion' exists
[2025-08-09 14:26:45.897]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 critical indexes for ReferenceData database
[2025-08-09 14:26:45.902]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_activity_type_code' is missing
[2025-08-09 14:26:45.908]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_craft_type_code' is missing
[2025-08-09 14:26:45.913]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_wilayas_code' is missing
[2025-08-09 14:26:45.919]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_cpi_dairas_code' is missing
[2025-08-09 14:26:45.927]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 14:26:45.933]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 14:26:45.939]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ReferenceData database
[2025-08-09 14:26:45.945]  	[DEBUG]		[DatabaseMigrationService]	Validating reference data integrity
[2025-08-09 14:26:45.952]  	[DEBUG]		[DatabaseMigrationService]	Reference data integrity validation completed
[2025-08-09 14:26:45.958]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ReferenceData database
[2025-08-09 14:26:45.964]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 14:26:45.970]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 14:26:45.980]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:45.991]  	[INFO]		[ServiceLocator]	Initializing archive database schema
[2025-08-09 14:26:46.000]  	[DEBUG]		[DatabaseMigrationService]	Starting database schema initialization
[2025-08-09 14:26:46.009]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:46.016]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA journal_mode = WAL;
[2025-08-09 14:26:46.028]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA synchronous = NORMAL;
[2025-08-09 14:26:46.045]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA cache_size = 10000;
[2025-08-09 14:26:46.055]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA temp_store = MEMORY;
[2025-08-09 14:26:46.071]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA auto_vacuum = INCREMENTAL;
[2025-08-09 14:26:46.078]  	[DEBUG]		[DatabaseService]	Executed: PRAGMA foreign_keys = ON;
[2025-08-09 14:26:46.084]  	[INFO]		[DatabaseService]	Database PRAGMA configuration completed successfully
[2025-08-09 14:26:46.090]  	[DEBUG]		[DatabaseMigrationService]	Database configuration applied successfully
[2025-08-09 14:26:46.096]  	[DEBUG]		[DatabaseMigrationService]	Current database schema version: 5, target version: 5
[2025-08-09 14:26:46.102]  	[DEBUG]		[DatabaseMigrationService]	Database schema is up to date
[2025-08-09 14:26:46.109]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 14:26:46.115]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:26:46.120]  	[DEBUG]		[DatabaseMigrationService]	Validating 4 required tables
[2025-08-09 14:26:46.128]  	[DEBUG]		[DatabaseMigrationService]	Table 'AddedEntities' exists
[2025-08-09 14:26:46.134]  	[DEBUG]		[DatabaseMigrationService]	Table 'UpdatedEntities' exists
[2025-08-09 14:26:46.140]  	[DEBUG]		[DatabaseMigrationService]	Table 'DeletedEntities' exists
[2025-08-09 14:26:46.146]  	[DEBUG]		[DatabaseMigrationService]	Table 'ArchiveSchemaVersion' exists
[2025-08-09 14:26:46.151]  	[DEBUG]		[DatabaseMigrationService]	Validating 3 critical indexes for ArchiveData database
[2025-08-09 14:26:46.158]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_added_entities_type_id' is missing
[2025-08-09 14:26:46.164]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_updated_entities_type_id' is missing
[2025-08-09 14:26:46.169]  	[WARN]		[DatabaseMigrationService]	Critical index 'idx_deleted_entities_type_id' is missing
[2025-08-09 14:26:46.177]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 14:26:46.183]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 14:26:46.189]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ArchiveData database
[2025-08-09 14:26:46.198]  	[DEBUG]		[DatabaseMigrationService]	Validating archive data integrity
[2025-08-09 14:26:46.210]  	[DEBUG]		[DatabaseMigrationService]	Archive data integrity validation completed
[2025-08-09 14:26:46.218]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ArchiveData database
[2025-08-09 14:26:46.224]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 14:26:46.231]  	[DEBUG]		[DatabaseMigrationService]	Database schema initialization completed successfully
[2025-08-09 14:26:46.236]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:46.244]  	[DEBUG]		[DatabaseMigrationService]	Starting comprehensive database schema validation
[2025-08-09 14:26:46.250]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:26:46.257]  	[DEBUG]		[DatabaseMigrationService]	Validating 11 required tables
[2025-08-09 14:26:46.273]  	[DEBUG]		[DatabaseMigrationService]	Table 'Clients' exists
[2025-08-09 14:26:46.279]  	[DEBUG]		[DatabaseMigrationService]	Table 'PhoneNumbers' exists
[2025-08-09 14:26:46.286]  	[DEBUG]		[DatabaseMigrationService]	Table 'Activities' exists
[2025-08-09 14:26:46.293]  	[DEBUG]		[DatabaseMigrationService]	Table 'CommercialActivityCodes' exists
[2025-08-09 14:26:46.299]  	[DEBUG]		[DatabaseMigrationService]	Table 'ProfessionNames' exists
[2025-08-09 14:26:46.305]  	[DEBUG]		[DatabaseMigrationService]	Table 'FileCheckStates' exists
[2025-08-09 14:26:46.312]  	[DEBUG]		[DatabaseMigrationService]	Table 'Notes' exists
[2025-08-09 14:26:46.318]  	[DEBUG]		[DatabaseMigrationService]	Table 'G12Check' exists
[2025-08-09 14:26:46.324]  	[DEBUG]		[DatabaseMigrationService]	Table 'BisCheck' exists
[2025-08-09 14:26:46.331]  	[DEBUG]		[DatabaseMigrationService]	Table 'UidSequences' exists
[2025-08-09 14:26:46.337]  	[DEBUG]		[DatabaseMigrationService]	Table 'SchemaVersion' exists
[2025-08-09 14:26:46.343]  	[DEBUG]		[DatabaseMigrationService]	Validating 8 critical indexes for ClientData database
[2025-08-09 14:26:46.349]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_name_fr' exists
[2025-08-09 14:26:46.357]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_client_uid' exists
[2025-08-09 14:26:46.362]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_client_uid' exists
[2025-08-09 14:26:46.368]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_file_check_states_activity_uid' exists
[2025-08-09 14:26:46.374]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_notes_activity_uid' exists
[2025-08-09 14:26:46.380]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_clients_arabic_search' exists
[2025-08-09 14:26:46.385]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_activities_type_client' exists
[2025-08-09 14:26:46.392]  	[DEBUG]		[DatabaseMigrationService]	Index 'idx_phone_numbers_composite' exists
[2025-08-09 14:26:46.398]  	[DEBUG]		[DatabaseMigrationService]	Validating basic database operations
[2025-08-09 14:26:46.404]  	[DEBUG]		[DatabaseMigrationService]	Basic database operations validated successfully
[2025-08-09 14:26:46.410]  	[DEBUG]		[DatabaseMigrationService]	Validating data integrity for ClientData database
[2025-08-09 14:26:46.415]  	[DEBUG]		[DatabaseMigrationService]	Validating client data integrity
[2025-08-09 14:26:46.421]  	[DEBUG]		[DatabaseMigrationService]	Client data integrity validation completed
[2025-08-09 14:26:46.427]  	[DEBUG]		[DatabaseMigrationService]	Data integrity validation completed for ClientData database
[2025-08-09 14:26:46.433]  	[DEBUG]		[DatabaseMigrationService]	Database schema validation completed successfully
[2025-08-09 14:26:46.439]  	[DEBUG]		[UIDGenerationService]	UIDGenerationService initialized
[2025-08-09 14:26:46.444]  	[DEBUG]		[ServiceLocator]	Service UIDGenerationService registered
[2025-08-09 14:26:46.450]  	[DEBUG]		[ArchiveDatabaseService]	ArchiveDatabaseService initialized
[2025-08-09 14:26:46.456]  	[DEBUG]		[ServiceLocator]	Service ArchiveDatabaseService registered
[2025-08-09 14:26:46.462]  	[DEBUG]		[ClientDatabaseService]	ClientDatabaseService initialized with audit logging support
[2025-08-09 14:26:46.468]  	[DEBUG]		[ServiceLocator]	Service ClientDatabaseService registered
[2025-08-09 14:26:46.476]  	[INFO]		[DatabasePerformanceMonitoringService]	DatabasePerformanceMonitoringService initialized
[2025-08-09 14:26:46.482]  	[DEBUG]		[ServiceLocator]	Service DatabasePerformanceMonitoringService registered
[2025-08-09 14:26:46.488]  	[INFO]		[EnhancedDatabaseService]	EnhancedDatabaseService initialized with performance monitoring
[2025-08-09 14:26:46.495]  	[DEBUG]		[ServiceLocator]	Service EnhancedDatabaseService registered
[2025-08-09 14:26:46.505]  	[INFO]		[DatabaseSchemaValidator]	DatabaseSchemaValidator initialized
[2025-08-09 14:26:46.510]  	[DEBUG]		[ServiceLocator]	Service DatabaseSchemaValidator registered
[2025-08-09 14:26:46.516]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBaseService initialized with reference database
[2025-08-09 14:26:46.522]  	[DEBUG]		[ServiceLocator]	Service ActivityTypeBaseService registered
[2025-08-09 14:26:46.528]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: ActivityTypeBaseService
[2025-08-09 14:26:46.535]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBaseService initialized with reference database
[2025-08-09 14:26:46.540]  	[DEBUG]		[ServiceLocator]	Service CraftTypeBaseService registered
[2025-08-09 14:26:46.546]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CraftTypeBaseService
[2025-08-09 14:26:46.552]  	[DEBUG]		[CpiLocationService]	CpiLocationService initialized with reference database
[2025-08-09 14:26:46.558]  	[DEBUG]		[ServiceLocator]	Service CpiLocationService registered
[2025-08-09 14:26:46.564]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: CpiLocationService
[2025-08-09 14:26:46.571]  	[DEBUG]		[ArchiveDatabaseService]	Creating archive database tables
[2025-08-09 14:26:46.577]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:26:46.584]  	[INFO]		[ArchiveDatabaseService]	Archive database tables created successfully
[2025-08-09 14:26:46.591]  	[INFO]		[ServiceLocator]	Initializing Arabic search services with exact prefix matching
[2025-08-09 14:26:46.596]  	[INFO]		[ArabicTextAnalyzer]	ArabicTextAnalyzer initialized with word frequency-based analysis
[2025-08-09 14:26:46.602]  	[DEBUG]		[ServiceLocator]	Service IArabicTextAnalyzer registered
[2025-08-09 14:26:46.609]  	[INFO]		[WordFrequencySearchService]	WordFrequencySearchService initialized with Arabic text analysis
[2025-08-09 14:26:46.615]  	[DEBUG]		[ServiceLocator]	Service WordFrequencySearchService registered
[2025-08-09 14:26:46.621]  	[INFO]		[ServiceLocator]	Arabic search services with exact prefix matching initialized successfully
[2025-08-09 14:26:46.628]  	[DEBUG]		[ServiceLocator]	Service FileCheckBusinessRuleService registered
[2025-08-09 14:26:46.634]  	[DEBUG]		[ServiceLocator]	Registered cacheable service: FileCheckBusinessRuleService
[2025-08-09 14:26:46.640]  	[DEBUG]		[ServiceLocator]	Service ClientValidationService registered
[2025-08-09 14:26:46.646]  	[DEBUG]		[ClientFolderManagementService]	ClientFolderManagementService initialized with base directory: C:\Users\<USER>\AppData\Roaming\UFU2\Clients
[2025-08-09 14:26:46.652]  	[DEBUG]		[ServiceLocator]	Service ClientFolderManagementService registered
[2025-08-09 14:26:46.659]  	[DEBUG]		[ServiceLocator]	Service DuplicateClientDetectionService registered
[2025-08-09 14:26:46.666]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:46.671]  	[DEBUG]		[ActivityTypeBaseService]	ActivityTypeBase table validation completed successfully
[2025-08-09 14:26:46.677]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:46.685]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:26:46.694]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:46.705]  	[DEBUG]		[CraftTypeBaseService]	CraftTypeBase table validation completed successfully
[2025-08-09 14:26:46.711]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:46.718]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:26:46.724]  	[INFO]		[ServiceLocator]	Seeding CPI location data from embedded resource
[2025-08-09 14:26:46.740]  	[INFO]		[CpiLocationService]	Starting CPI location data seeding from embedded JSON resource
[2025-08-09 14:26:46.819]  	[INFO]		[CpiLocationService]	Starting to seed 58 wilayas and 281 dairas
[2025-08-09 14:26:46.829]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:26:46.858]  	[DEBUG]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 14:26:46.865]  	[INFO]		[CpiLocationService]	Successfully seeded 58 wilayas
[2025-08-09 14:26:46.875]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:26:46.897]  	[DEBUG]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 14:26:46.904]  	[INFO]		[CpiLocationService]	Successfully seeded 281 dairas
[2025-08-09 14:26:46.911]  	[DEBUG]		[CpiLocationService]	All caches cleared
[2025-08-09 14:26:46.917]  	[INFO]		[CpiLocationService]	CPI location data seeding completed successfully
[2025-08-09 14:26:46.926]  	[INFO]		[CpiLocationService]	Displaying user success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 14:26:46.942]  	[DEBUG]		[ToastService]	Theme resources applied to desktop toast window
[2025-08-09 14:26:47.351]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:26:47.472]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 14:26:47.481]  	[DEBUG]		[ToastService]	Desktop toast notifier initialized with theme support
[2025-08-09 14:26:47.492]  	[DEBUG]		[ToastService]	Displaying Success toast: تم التحميل بنجاح - تم تحميل البيانات الجغرافية بنجاح. الولايات: 58، الدوائر: 281
[2025-08-09 14:26:47.612]  	[INFO]		[ToastNotification]	Toast notification created: Success - تم التحميل بنجاح
[2025-08-09 14:26:47.629]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 14:26:47.649]  	[INFO]		[ToastService]	Desktop toast displayed: Success - تم التحميل بنجاح
[2025-08-09 14:26:47.664]  	[INFO]		[ServiceLocator]	Successfully seeded CPI location data - Wilayas: 58, Dairas: 281
[2025-08-09 14:26:47.683]  	[DEBUG]		[DatabaseMigrationService]	Collecting database statistics for ClientData database
[2025-08-09 14:26:47.690]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:26:47.701]  	[DEBUG]		[DatabaseMigrationService]	Client database stats: 1 clients, 0 activities
[2025-08-09 14:26:47.724]  	[DEBUG]		[DatabaseMigrationService]	Database stats collected for ClientData: 0.27 MB MB size
[2025-08-09 14:26:47.731]  	[INFO]		[ServiceLocator]	Three-database architecture initialized - Client DB Version: 5, Tables: 12, Indexes: 52
[2025-08-09 14:26:47.739]  	[DEBUG]		[ServiceLocator]	All required database services are registered
[2025-08-09 14:26:47.748]  	[INFO]		[CacheCoordinatorService]	CacheCoordinatorService initialized with health monitoring and coordinated cleanup
[2025-08-09 14:26:47.755]  	[DEBUG]		[ServiceLocator]	Service CacheCoordinatorService registered
[2025-08-09 14:26:47.763]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ValidationService
[2025-08-09 14:26:47.771]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: ActivityTypeBaseService
[2025-08-09 14:26:47.779]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CraftTypeBaseService
[2025-08-09 14:26:47.787]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: CpiLocationService
[2025-08-09 14:26:47.796]  	[DEBUG]		[CacheCoordinatorService]	Registered cacheable service for coordination: FileCheckBusinessRuleService
[2025-08-09 14:26:47.803]  	[INFO]		[ServiceLocator]	Cache coordinator initialized with 5 cacheable services
[2025-08-09 14:26:47.810]  	[INFO]		[CacheMonitoringService]	CacheMonitoringService initialized with monitoring, cleanup, and statistics collection
[2025-08-09 14:26:47.816]  	[DEBUG]		[ServiceLocator]	Service CacheMonitoringService registered
[2025-08-09 14:26:47.823]  	[INFO]		[ServiceLocator]	Cache monitoring service initialized
[2025-08-09 14:26:47.830]  	[INFO]		[MemoryPressureHandler]	MemoryPressureHandler initialized with memory monitoring
[2025-08-09 14:26:47.837]  	[DEBUG]		[ServiceLocator]	Service MemoryPressureHandler registered
[2025-08-09 14:26:47.844]  	[INFO]		[ServiceLocator]	Memory pressure handler initialized
[2025-08-09 14:26:47.854]  	[INFO]		[CacheCoordinatorService]	Coordinating cache warmup for 5 services
[2025-08-09 14:26:47.870]  	[DEBUG]		[ActivityTypeBaseService]	Starting cache warmup for ActivityTypeBaseService
[2025-08-09 14:26:47.897]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:47.960]  	[DEBUG]		[ActivityTypeBaseService]	Cached 1028 activity types (Cache hits: 0, misses: 1)
[2025-08-09 14:26:47.973]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:48.008]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:48.026]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'تجاري' returned 0 results (Cache hits: 0, misses: 1)
[2025-08-09 14:26:48.038]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:48.045]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:48.052]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'حرفي' returned 0 results (Cache hits: 0, misses: 2)
[2025-08-09 14:26:48.059]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:48.071]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:48.077]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: MainWindow_CommonData (Priority: Low)
[2025-08-09 14:26:48.080]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'مهني' returned 0 results (Cache hits: 0, misses: 3)
[2025-08-09 14:26:48.089]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: MainWindow (MainWindow)
[2025-08-09 14:26:48.094]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:48.098]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 1, misses: 1)
[2025-08-09 14:26:48.102]  	[INFO]		[MainWindow]	Background services initialized successfully
[2025-08-09 14:26:48.110]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:48.114]  	[INFO]		[MainWindow]	Background common data preloading completed
[2025-08-09 14:26:48.126]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Commercial' returned 0 results (Cache hits: 0, misses: 4)
[2025-08-09 14:26:48.131]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: MainWindow_CommonData in 33ms
[2025-08-09 14:26:48.135]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:48.145]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:48.152]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Craft' returned 0 results (Cache hits: 0, misses: 5)
[2025-08-09 14:26:48.159]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:48.165]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:48.178]  	[DEBUG]		[ActivityTypeBaseService]	Search for 'Professional' returned 0 results (Cache hits: 0, misses: 6)
[2025-08-09 14:26:48.186]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:48.193]  	[INFO]		[ActivityTypeBaseService]	Cache warmup completed. Search cache hits: 0, Data cache hits: 1
[2025-08-09 14:26:48.204]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ActivityTypeBaseService in 333ms
[2025-08-09 14:26:48.240]  	[DEBUG]		[ValidationService]	Starting cache warmup for ValidationService
[2025-08-09 14:26:48.294]  	[INFO]		[ValidationService]	Cache warmup completed. Cache hits: 0, Cache misses: 2
[2025-08-09 14:26:48.300]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for ValidationService in 61ms
[2025-08-09 14:26:48.309]  	[DEBUG]		[FileCheckBusinessRuleService]	Starting cache warmup for FileCheckBusinessRuleService
[2025-08-09 14:26:48.318]  	[INFO]		[FileCheckBusinessRuleService]	Cache warmup completed. Cache hits: 0, Cache misses: 8
[2025-08-09 14:26:48.325]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for FileCheckBusinessRuleService in 18ms
[2025-08-09 14:26:48.333]  	[DEBUG]		[CpiLocationService]	Starting cache warmup
[2025-08-09 14:26:48.342]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:26:48.350]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 58 wilayas
[2025-08-09 14:26:48.362]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:26:48.372]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 23 dairas for wilaya 16
[2025-08-09 14:26:48.379]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:26:48.385]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 10 dairas for wilaya 31
[2025-08-09 14:26:48.393]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:26:48.400]  	[DEBUG]		[CpiLocationService]	Retrieved and cached 3 dairas for wilaya 25
[2025-08-09 14:26:48.408]  	[DEBUG]		[CpiLocationService]	Cache warmup completed
[2025-08-09 14:26:48.415]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CpiLocationService in 83ms
[2025-08-09 14:26:48.423]  	[DEBUG]		[CraftTypeBaseService]	Starting cache warmup for CraftTypeBaseService
[2025-08-09 14:26:48.431]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:48.441]  	[DEBUG]		[CraftTypeBaseService]	Cached 337 craft types (Cache hits: 0, misses: 1)
[2025-08-09 14:26:48.448]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:48.456]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:48.463]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تجاري' (Cache hits: 0, misses: 1)
[2025-08-09 14:26:48.469]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:48.477]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:48.498]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'حرفي' (Cache hits: 0, misses: 2)
[2025-08-09 14:26:48.507]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:48.514]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:48.522]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'مهني' (Cache hits: 0, misses: 3)
[2025-08-09 14:26:48.527]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:48.533]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:48.540]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'صناعة' (Cache hits: 0, misses: 4)
[2025-08-09 14:26:48.546]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:48.553]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:48.560]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'تقليدي' (Cache hits: 0, misses: 5)
[2025-08-09 14:26:48.566]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:48.572]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:48.578]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Commercial' (Cache hits: 0, misses: 6)
[2025-08-09 14:26:48.584]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:48.591]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:48.597]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Craft' (Cache hits: 0, misses: 7)
[2025-08-09 14:26:48.603]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:48.611]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:48.617]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Professional' (Cache hits: 0, misses: 8)
[2025-08-09 14:26:48.628]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:48.634]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:26:48.641]  	[DEBUG]		[CraftTypeBaseService]	Found 0 craft types matching 'Traditional' (Cache hits: 0, misses: 9)
[2025-08-09 14:26:48.647]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:26:48.653]  	[INFO]		[CraftTypeBaseService]	Cache warmup completed for CraftTypeBaseService
[2025-08-09 14:26:48.661]  	[INFO]		[CacheCoordinatorService]	Cache warmup completed for CraftTypeBaseService in 239ms
[2025-08-09 14:26:48.667]  	[INFO]		[ServiceLocator]	=== Service Registration Status ===
[2025-08-09 14:26:48.674]  	[INFO]		[ServiceLocator]	DatabaseService: ✓ Registered
[2025-08-09 14:26:48.680]  	[INFO]		[ServiceLocator]	DatabaseMigrationService: ✓ Registered
[2025-08-09 14:26:48.686]  	[INFO]		[ServiceLocator]	UIDGenerationService: ✓ Registered
[2025-08-09 14:26:48.692]  	[INFO]		[ServiceLocator]	ClientDatabaseService: ✓ Registered
[2025-08-09 14:26:48.698]  	[INFO]		[ServiceLocator]	DatabasePerformanceMonitoringService: ✓ Registered
[2025-08-09 14:26:48.704]  	[INFO]		[ServiceLocator]	EnhancedDatabaseService: ✓ Registered
[2025-08-09 14:26:48.712]  	[INFO]		[ServiceLocator]	DatabaseSchemaValidator: ✓ Registered
[2025-08-09 14:26:48.722]  	[INFO]		[ServiceLocator]	ActivityTypeBaseService: ✓ Registered
[2025-08-09 14:26:48.728]  	[INFO]		[ServiceLocator]	ClientValidationService: ✓ Registered
[2025-08-09 14:26:48.735]  	[INFO]		[ServiceLocator]	ValidationService: ✓ Registered
[2025-08-09 14:26:48.742]  	[INFO]		[ServiceLocator]	IToastService: ✓ Registered
[2025-08-09 14:26:48.748]  	[INFO]		[ServiceLocator]	IWindowChromeService: ✓ Registered
[2025-08-09 14:26:48.755]  	[INFO]		[ServiceLocator]	Total registered services: 32
[2025-08-09 14:26:48.762]  	[INFO]		[ServiceLocator]	=== End Service Registration Status ===
[2025-08-09 14:26:48.769]  	[DEBUG]		[ServiceLocator]	Database services initialized successfully
[2025-08-09 14:26:48.776]  	[DEBUG]		[App]	UFU2 Application startup completed in 0ms
[2025-08-09 14:26:48.919]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: False
[2025-08-09 14:26:48.927]  	[DEBUG]		[TitleBarBehavior]	Title bar drag enabled for Grid
[2025-08-09 14:26:48.935]  	[DEBUG]		[TitleBarBehavior]	Event handlers updated - Drag: True, DoubleClick: True
[2025-08-09 14:26:48.943]  	[DEBUG]		[TitleBarBehavior]	Title bar double-click maximize enabled for Grid
[2025-08-09 14:26:48.955]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 14:26:48.962]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:48.971]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 14:26:48.978]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:48.987]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: False)
[2025-08-09 14:26:48.993]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:49.083]  	[DEBUG]		[MainWindow]	MainWindow loaded, initializing title bar behaviors
[2025-08-09 14:26:49.089]  	[DEBUG]		[MainWindow]	Title bar behavior target window set successfully
[2025-08-09 14:26:49.835]  	[DEBUG]		[CustomWindowChromeViewModel]	PropertyChanged Performance - Total: 3, Batched: 3, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: WindowTitle(2), WindowIcon(1)
[2025-08-09 14:26:49.857]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:49.865]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:49.874]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:49.880]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:49.887]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:49.894]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:50.603]  	[INFO]		[ToastNotification]	Toast notification closed: Success - تم التحميل بنجاح
[2025-08-09 14:26:52.436]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:52.586]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:52.755]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:53.190]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:26:53.508]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:26:53.689]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:26:54.172]  	[INFO]		[MainWindow]	User clicked AddUserButton
[2025-08-09 14:26:54.523]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientDialog (NewClientView, OnDemand)
[2025-08-09 14:26:54.774]  	[DEBUG]		[ViewLoadingMonitoringService]	Started monitoring view loading: NewClientView (NewClientView, Immediate)
[2025-08-09 14:26:54.989]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 14:26:55.275]  	[DEBUG]		[ResourceManager]	Registered resource: NPersonalViewModel_12837735_638903428152755013 (BaseViewModel) for NPersonalViewModel
[2025-08-09 14:26:55.605]  	[DEBUG]		[NPersonalViewModel]	BaseViewModel memory management initialized for NPersonalViewModel
[2025-08-09 14:26:55.968]  	[DEBUG]		[NPersonalViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:26:56.233]  	[DEBUG]		[ResourceManager]	Registered resource: PersonalInformationViewModel_48430751_638903428162333926 (BaseViewModel) for PersonalInformationViewModel
[2025-08-09 14:26:56.533]  	[DEBUG]		[PersonalInformationViewModel]	BaseViewModel memory management initialized for PersonalInformationViewModel
[2025-08-09 14:26:56.815]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:26:57.106]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel initialized
[2025-08-09 14:26:57.486]  	[DEBUG]		[ResourceManager]	Registered resource: ContactInformationViewModel_33223577_638903428174862817 (BaseViewModel) for ContactInformationViewModel
[2025-08-09 14:26:57.728]  	[DEBUG]		[ContactInformationViewModel]	BaseViewModel memory management initialized for ContactInformationViewModel
[2025-08-09 14:26:58.001]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:26:58.306]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 14:26:58.673]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel initialized
[2025-08-09 14:26:58.978]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage control initialized with optimization
[2025-08-09 14:26:59.140]  	[INFO]		[NActivityTabView]	NActivityTabView initialized with optimization
[2025-08-09 14:26:59.384]  	[DEBUG]		[ResourceManager]	Registered resource: NewClientViewModel_40827483_638903428193846903 (BaseViewModel) for NewClientViewModel
[2025-08-09 14:26:59.494]  	[DEBUG]		[NewClientViewModel]	BaseViewModel memory management initialized for NewClientViewModel
[2025-08-09 14:26:59.660]  	[DEBUG]		[NewClientViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:27:00.041]  	[DEBUG]		[ResourceManager]	Registered resource: PersonalInformationViewModel_31903028_638903428200419751 (BaseViewModel) for PersonalInformationViewModel
[2025-08-09 14:27:00.364]  	[DEBUG]		[PersonalInformationViewModel]	BaseViewModel memory management initialized for PersonalInformationViewModel
[2025-08-09 14:27:00.510]  	[DEBUG]		[PersonalInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:27:00.798]  	[DEBUG]		[PersonalInformationViewModel]	PersonalInformationViewModel initialized
[2025-08-09 14:27:01.009]  	[DEBUG]		[ResourceManager]	Registered resource: ContactInformationViewModel_18691797_638903428210092455 (BaseViewModel) for ContactInformationViewModel
[2025-08-09 14:27:01.245]  	[DEBUG]		[ContactInformationViewModel]	BaseViewModel memory management initialized for ContactInformationViewModel
[2025-08-09 14:27:01.596]  	[DEBUG]		[ContactInformationViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:27:01.909]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 14:27:02.082]  	[DEBUG]		[ContactInformationViewModel]	ContactInformationViewModel initialized
[2025-08-09 14:27:02.409]  	[DEBUG]		[ResourceManager]	Registered resource: ActivityManagementViewModel_34008447_638903428224094039 (BaseViewModel) for ActivityManagementViewModel
[2025-08-09 14:27:02.574]  	[DEBUG]		[ActivityManagementViewModel]	BaseViewModel memory management initialized for ActivityManagementViewModel
[2025-08-09 14:27:03.070]  	[DEBUG]		[ActivityManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:27:03.624]  	[DEBUG]		[ActivityManagementViewModel]	Set default activity status for MainCommercial
[2025-08-09 14:27:04.291]  	[DEBUG]		[CpiLocationService]	Retrieved wilayas from cache
[2025-08-09 14:27:05.193]  	[INFO]		[ActivityManagementViewModel]	Loaded 58 CPI Wilayas
[2025-08-09 14:27:06.193]  	[DEBUG]		[ActivityManagementViewModel]	ActivityManagementViewModel initialized
[2025-08-09 14:27:06.576]  	[DEBUG]		[ResourceManager]	Registered resource: NotesManagementViewModel_37640571_638903428265762085 (BaseViewModel) for NotesManagementViewModel
[2025-08-09 14:27:06.777]  	[DEBUG]		[NotesManagementViewModel]	BaseViewModel memory management initialized for NotesManagementViewModel
[2025-08-09 14:27:07.023]  	[DEBUG]		[NotesManagementViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:27:07.246]  	[DEBUG]		[UFU2BulkObservableCollection]	UFU2BulkObservableCollection created with smart coalescing for client data management
[2025-08-09 14:27:07.522]  	[DEBUG]		[NotesManagementViewModel]	NotesManagementViewModel initialized
[2025-08-09 14:27:07.732]  	[DEBUG]		[NewClientViewModel]	Services initialized successfully
[2025-08-09 14:27:07.885]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 14:27:08.379]  	[DEBUG]		[NewClientViewModel]	NewClientViewModel initialized with component ViewModels
[2025-08-09 14:27:08.851]  	[INFO]		[NewClientView]	Phone numbers synchronization setup completed (bidirectional sync disabled for tab independence)
[2025-08-09 14:27:09.215]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 14:27:09.547]  	[INFO]		[NewClientView]	NameFr real-time synchronization setup completed with optimization
[2025-08-09 14:27:09.901]  	[INFO]		[NewClientView]	Save data transfer mechanism setup completed
[2025-08-09 14:27:10.094]  	[DEBUG]		[ViewMemoryOptimizationService]	Registered view for memory tracking: NewClientView (NewClientView)
[2025-08-09 14:27:10.308]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ActivityTypes (Priority: Low)
[2025-08-09 14:27:10.463]  	[DEBUG]		[ActivityTypeBaseService]	Returning cached activity types (Cache hits: 2, misses: 1)
[2025-08-09 14:27:10.685]  	[DEBUG]		[NewClientView]	Background activity types preloading completed
[2025-08-09 14:27:10.766]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:10.909]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ActivityTypes in 447ms
[2025-08-09 14:27:11.032]  	[INFO]		[ClientValidationService]	Client validation completed. Valid: False, Errors: 1
[2025-08-09 14:27:11.167]  	[DEBUG]		[NPersonalViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:11.519]  	[DEBUG]		[NewClientView]	Background validation rules preloading completed
[2025-08-09 14:27:11.624]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:11.707]  	[DEBUG]		[BackgroundViewInitializationService]	Completed background task: NewClientView_ValidationRules in 1064ms
[2025-08-09 14:27:11.837]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:11.973]  	[DEBUG]		[NewClientViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:12.105]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:12.211]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:12.445]  	[DEBUG]		[ActivityManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:12.579]  	[DEBUG]		[NotesManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:10.613]  	[DEBUG]		[BackgroundViewInitializationService]	Queued background initialization task: NewClientView_ValidationRules (Priority: Low)
[2025-08-09 14:27:12.782]  	[DEBUG]		[NewClientView]	Queued background initialization tasks for NewClientView
[2025-08-09 14:27:12.942]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientView - 18165ms (Success: True)
[2025-08-09 14:27:13.087]  	[DEBUG]		[ViewLoadingMonitoringService]	Completed view loading monitoring: NewClientDialog - 18566ms (Success: True)
[2025-08-09 14:27:13.387]  	[DEBUG]		[MainWindow]	Opening NewClientView dialog with optimized size: 900x560 (Height-based width calculation)
[2025-08-09 14:27:13.810]  	[DEBUG]		[ProfileImageConverter]	Male default image loaded and cached
[2025-08-09 14:27:14.340]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 14:27:16.871]  	[DEBUG]		[ClientProfileImage]	ClientProfileImage loaded - Gender: 0
[2025-08-09 14:27:17.329]  	[INFO]		[NActivityTabView]	NActivityTabView loaded successfully
[2025-08-09 14:27:19.462]  	[DEBUG]		[CustomWindowChromeViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:27:19.578]  	[DEBUG]		[NPersonalViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:27:19.734]  	[DEBUG]		[PersonalInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:27:19.874]  	[DEBUG]		[ContactInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:27:20.137]  	[DEBUG]		[NewClientViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:27:20.412]  	[DEBUG]		[PersonalInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:27:20.640]  	[DEBUG]		[ContactInformationViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:27:20.777]  	[DEBUG]		[ActivityManagementViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:27:20.916]  	[DEBUG]		[NotesManagementViewModel]	Application activated - UI state tracking updated
[2025-08-09 14:27:21.045]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 3, Time since interaction: 1581.7114ms
[2025-08-09 14:27:21.176]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:21.255]  	[DEBUG]		[NPersonalViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: PhoneNumbersCollection(1)
[2025-08-09 14:27:21.316]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 1738.1934ms
[2025-08-09 14:27:21.367]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:21.433]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1699.3073ms
[2025-08-09 14:27:21.489]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:21.555]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1681.4763ms
[2025-08-09 14:27:21.634]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:21.699]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1561.7291ms
[2025-08-09 14:27:21.825]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:21.914]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1502.5947ms
[2025-08-09 14:27:21.967]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:22.021]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1381.483ms
[2025-08-09 14:27:22.085]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:22.207]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1430.3044ms
[2025-08-09 14:27:22.273]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:22.368]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 0, Time since interaction: 1451.3496ms
[2025-08-09 14:27:22.431]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:22.508]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:22.534]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:22.565]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:22.591]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:22.624]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:22.737]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:22.934]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 3, Time since interaction: 3472.7121ms
[2025-08-09 14:27:22.968]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:23.011]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 3433.652ms
[2025-08-09 14:27:23.049]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:23.098]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 3363.4476ms
[2025-08-09 14:27:23.174]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:23.237]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 3363.5993ms
[2025-08-09 14:27:23.262]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:23.277]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 3139.8704ms
[2025-08-09 14:27:23.294]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:23.322]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2910.4566ms
[2025-08-09 14:27:23.331]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:23.380]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2740.0249ms
[2025-08-09 14:27:23.477]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:23.539]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2762.3696ms
[2025-08-09 14:27:23.581]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:23.621]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 0, Time since interaction: 2704.4604ms
[2025-08-09 14:27:23.638]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:23.714]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:23.738]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:23.761]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:23.797]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:23.841]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:23.900]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:24.001]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:24.045]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:24.077]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:24.100]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:24.129]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:24.155]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:24.362]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:24.378]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:24.391]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:24.399]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:24.415]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:24.428]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:24.447]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:24.475]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:24.550]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:24.589]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:24.612]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:24.629]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:26.103]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 14:27:26.147]  	[DEBUG]		[NPersonalViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 14:27:26.287]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 183.8776ms
[2025-08-09 14:27:26.343]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:26.520]  	[DEBUG]		[NPersonalViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(1)
[2025-08-09 14:27:26.581]  	[DEBUG]		[PersonalInformationViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(1)
[2025-08-09 14:27:26.643]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 580.2036ms
[2025-08-09 14:27:26.663]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:26.765]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-09 14:27:26.870]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-09 14:27:26.905]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-09 14:27:26.940]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-09 14:27:26.977]  	[DEBUG]		[NewClientView]	NameFr synced to main ViewModel: 'DRIDI YACINE'
[2025-08-09 14:27:27.011]  	[DEBUG]		[NPersonalView]	Checking for duplicate clients with NameFr: 'DRIDI YACINE'
[2025-08-09 14:27:27.067]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:27:27.247]  	[INFO]		[DuplicateClientDetectionService]	Found 1 duplicate clients for NameFr: DRIDI YACINE
[2025-08-09 14:27:27.266]  	[INFO]		[NPersonalView]	Found 1 duplicate clients for 'DRIDI YACINE'
[2025-08-09 14:27:27.299]  	[DEBUG]		[ResourceManager]	Registered resource: DuplicateClientDetectionDialogViewModel_19385714_638903428472992864 (BaseViewModel) for DuplicateClientDetectionDialogViewModel
[2025-08-09 14:27:27.320]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	BaseViewModel memory management initialized for DuplicateClientDetectionDialogViewModel
[2025-08-09 14:27:27.340]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Enhanced BaseViewModel initialized with smart batching and UI state detection
[2025-08-09 14:27:27.361]  	[DEBUG]		[DuplicateClientDetectionDialog]	DuplicateClientDetectionDialog ViewModel initialized with 1 duplicate clients
[2025-08-09 14:27:27.378]  	[INFO]		[DuplicateClientDetectionDialog]	Created DuplicateClientDetectionDialog for 'DRIDI YACINE' with 1 duplicates
[2025-08-09 14:27:27.426]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-09 14:27:27.449]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-09 14:27:27.472]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-09 14:27:27.493]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-09 14:27:27.506]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 14:27:27.525]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Processed 4 Normal priority property notifications
[2025-08-09 14:27:27.736]  	[DEBUG]		[NewClientViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 14:27:27.791]  	[DEBUG]		[PersonalInformationViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(1)
[2025-08-09 14:27:27.818]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:27.837]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:27.852]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:27.873]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:27.891]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:27.899]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:27.917]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 2, Time since interaction: 411.5065ms
[2025-08-09 14:27:27.933]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:27.963]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 1198.0019ms
[2025-08-09 14:27:28.023]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:28.119]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:28.135]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:28.154]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:28.170]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:28.182]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:28.195]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:28.240]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:28.253]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:28.267]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:28.280]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:28.291]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:28.312]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:28.342]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 4, Time since interaction: 981.6993ms
[2025-08-09 14:27:28.366]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:28.944]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2840.9738ms
[2025-08-09 14:27:28.954]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:28.971]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2908.7294ms
[2025-08-09 14:27:28.978]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:29.036]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2270.9942ms
[2025-08-09 14:27:29.049]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:29.411]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 4, Time since interaction: 2050.3755ms
[2025-08-09 14:27:29.418]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:29.427]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:29.436]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:29.448]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:29.456]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:29.463]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:29.472]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:29.485]  	[INFO]		[DuplicateClientDetectionDialogViewModel]	User chose to create new client
[2025-08-09 14:27:29.495]  	[DEBUG]		[DuplicateClientDetectionDialog]	Dialog close requested with result: False
[2025-08-09 14:27:29.511]  	[INFO]		[NPersonalView]	User chose to create new client
[2025-08-09 14:27:29.537]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 14:27:29.697]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:29.704]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:29.712]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:29.720]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:29.731]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:29.739]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:29.986]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 2, Time since interaction: 2479.7964ms
[2025-08-09 14:27:29.992]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:30.524]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 1029.9213ms
[2025-08-09 14:27:30.531]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:31.205]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:31.212]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:31.222]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:31.235]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:31.242]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:31.249]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:31.297]  	[INFO]		[NewClientView]	Transferred PersonalInfo data - NameFr: 'DRIDI YACINE', NameAr: '', BirthDate: '', Gender: 0
[2025-08-09 14:27:31.308]  	[INFO]		[NewClientView]	Transferred 0 phone numbers to ViewModel for saving
[2025-08-09 14:27:31.318]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation passed
[2025-08-09 14:27:31.328]  	[DEBUG]		[ContactInformationViewModel]	Contact information validation passed
[2025-08-09 14:27:31.338]  	[DEBUG]		[ActivityManagementViewModel]	Activity validation passed
[2025-08-09 14:27:31.360]  	[DEBUG]		[NotesManagementViewModel]	Notes validation passed
[2025-08-09 14:27:31.379]  	[DEBUG]		[DatabaseService]	Reused pooled database connection
[2025-08-09 14:27:31.397]  	[DEBUG]		[UIDGenerationService]	Extracted first letter 'D' from name 'DRIDI YACINE' (using existing transaction)
[2025-08-09 14:27:31.425]  	[DEBUG]		[UIDGenerationService]	Generated sequence 2 for Client with prefix 'D'
[2025-08-09 14:27:31.432]  	[DEBUG]		[UIDGenerationService]	Cached sequence 2 for Client:D
[2025-08-09 14:27:31.445]  	[DEBUG]		[UIDGenerationService]	Generated Client UID: D02 for name: DRIDI YACINE (within existing transaction)
[2025-08-09 14:27:31.465]  	[DEBUG]		[UIDGenerationService]	Sequence cache cleared
[2025-08-09 14:27:31.472]  	[INFO]		[ClientDatabaseService]	Client created successfully with UID: D02
[2025-08-09 14:27:31.482]  	[DEBUG]		[DatabaseService]	Returned connection to pool
[2025-08-09 14:27:31.502]  	[INFO]		[NewClientViewModel]	Client saved successfully: D02
[2025-08-09 14:27:31.502]  	[DEBUG]		[DatabaseService]	New database connection created (non-pooled)
[2025-08-09 14:27:31.518]  	[INFO]		[ErrorManager]	Displaying user success toast: نجح الحفظ - تم إنشاء العميل بنجاح
[2025-08-09 14:27:31.533]  	[DEBUG]		[ArchiveDatabaseService]	Logged data addition: Client[D02].ClientData = {"uid":"D02","nameFr":"DRIDI YACINE","nameAr":null,"birthDate":null,"birthPlace":null,"gender":0,"address":null,"nationalId":null,"createdAt":"09/08/2025 14:27:31","updatedAt":"09/08/2025 14:27:31"}
[2025-08-09 14:27:31.537]  	[DEBUG]		[ToastService]	Displaying Success toast: نجح الحفظ - تم إنشاء العميل بنجاح
[2025-08-09 14:27:31.564]  	[INFO]		[ToastNotification]	Toast notification created: Success - نجح الحفظ
[2025-08-09 14:27:31.590]  	[DEBUG]		[ToastService]	Toast window z-order set to topmost successfully
[2025-08-09 14:27:31.609]  	[INFO]		[ToastService]	Desktop toast displayed: Success - نجح الحفظ
[2025-08-09 14:27:31.621]  	[DEBUG]		[PersonalInformationViewModel]	Personal information cleared
[2025-08-09 14:27:31.631]  	[DEBUG]		[ContactInformationViewModel]	Contact information cleared
[2025-08-09 14:27:31.641]  	[DEBUG]		[ActivityManagementViewModel]	Set default activity status for MainCommercial
[2025-08-09 14:27:31.654]  	[DEBUG]		[ActivityManagementViewModel]	Activity management data cleared
[2025-08-09 14:27:31.674]  	[DEBUG]		[NotesManagementViewModel]	Notes cleared
[2025-08-09 14:27:31.683]  	[DEBUG]		[NewClientViewModel]	Cleared editing state - returned to new client creation mode
[2025-08-09 14:27:31.690]  	[INFO]		[NewClientViewModel]	Dialog closed and data cleared
[2025-08-09 14:27:31.698]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 14:27:31.745]  	[DEBUG]		[PersonalInformationViewModel]	Personal information validation failed: NameFr is required
[2025-08-09 14:27:31.759]  	[DEBUG]		[PersonalInformationViewModel]	Processed 1 Normal priority property notifications
[2025-08-09 14:27:31.779]  	[DEBUG]		[ActivityManagementViewModel]	Processed 6 Normal priority property notifications
[2025-08-09 14:27:31.792]  	[DEBUG]		[NotesManagementViewModel]	Processed 3 Normal priority property notifications
[2025-08-09 14:27:31.799]  	[DEBUG]		[NewClientViewModel]	Processed 4 Normal priority property notifications
[2025-08-09 14:27:31.859]  	[DEBUG]		[NewClientViewModel]	PropertyChanged Performance - Total: 9, Batched: 5, Immediate: 4, Efficiency: 55.6%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(2), G12DisplayText(1), BISDisplayText(1), NotesDisplayText(1)
[2025-08-09 14:27:31.874]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 1, Time since interaction: 253.3271ms
[2025-08-09 14:27:31.887]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:31.902]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 6, Time since interaction: 248.3381ms
[2025-08-09 14:27:31.924]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:31.942]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 6, Time since interaction: 267.7978ms
[2025-08-09 14:27:31.963]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:31.979]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2485.3896ms
[2025-08-09 14:27:32.005]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:32.043]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Active - Focus: True, Notifications/sec: 7, Time since interaction: 251.0259ms
[2025-08-09 14:27:32.053]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Responsive based on UI state: Active
[2025-08-09 14:27:32.069]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:32.083]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:32.094]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:32.108]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:32.115]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:32.123]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:32.330]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	PropertyChanged Performance - Total: 5, Batched: 5, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: SelectedClient(2), ContentMessage(1), HeaderText(1), DuplicateClients(1)
[2025-08-09 14:27:32.984]  	[DEBUG]		[PersonalInformationViewModel]	PropertyChanged Performance - Total: 1, Batched: 1, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NameFr(1)
[2025-08-09 14:27:32.995]  	[DEBUG]		[ActivityManagementViewModel]	PropertyChanged Performance - Total: 6, Batched: 6, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: CurrentActivity(1), CurrentFileCheckStates(1), G12SelectedYears(1), BISSelectedYears(1), G12DisplayText(1)
[2025-08-09 14:27:33.007]  	[DEBUG]		[NotesManagementViewModel]	PropertyChanged Performance - Total: 6, Batched: 6, Immediate: 0, Efficiency: 100.0%, High Activity: False, Bulk Updates: 0 (Success: 0, Failed: 0), Bulk Success Rate: 0.0%, Avg Bulk Time: 0.0ms, Top Properties: NotesDisplayText(2), NotesCount(2), HasNotes(2)
[2025-08-09 14:27:34.013]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 1, Time since interaction: 2391.9245ms
[2025-08-09 14:27:34.020]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:34.029]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 6, Time since interaction: 2375.0943ms
[2025-08-09 14:27:34.037]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:34.046]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 6, Time since interaction: 2371.7029ms
[2025-08-09 14:27:34.053]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:34.074]  	[DEBUG]		[NewClientViewModel]	UI state changed from Active to Idle - Focus: True, Notifications/sec: 7, Time since interaction: 2282.369ms
[2025-08-09 14:27:34.085]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Responsive to Balanced based on UI state: Idle
[2025-08-09 14:27:34.464]  	[DEBUG]		[CustomWindowChromeViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:34.497]  	[DEBUG]		[NPersonalViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:34.506]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:34.513]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:34.522]  	[DEBUG]		[NewClientViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:34.529]  	[DEBUG]		[PersonalInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:34.542]  	[DEBUG]		[ContactInformationViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:34.550]  	[DEBUG]		[ActivityManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:34.566]  	[DEBUG]		[NotesManagementViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:34.600]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Application deactivated - UI state tracking updated
[2025-08-09 14:27:34.637]  	[INFO]		[ToastNotification]	Toast notification closed: Success - نجح الحفظ
[2025-08-09 14:27:34.698]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:34.715]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMinimizeCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:34.844]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:34.946]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteMaximizeRestoreCommand: True - ResizeMode: CanResizeWithGrip
[2025-08-09 14:27:34.956]  	[DEBUG]		[CustomWindowChromeViewModel]	Using stored target window: UFU Client Management (IsLoaded: True)
[2025-08-09 14:27:34.966]  	[DEBUG]		[CustomWindowChromeViewModel]	CanExecuteCloseCommand: True - window available: True
[2025-08-09 14:27:35.022]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 1, Time since interaction: 3400.7078ms
[2025-08-09 14:27:35.030]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:27:35.039]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 0, Time since interaction: 14399.668ms
[2025-08-09 14:27:35.046]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:27:35.085]  	[DEBUG]		[ActivityManagementViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 6, Time since interaction: 3431.5584ms
[2025-08-09 14:27:35.093]  	[DEBUG]		[ActivityManagementViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:27:35.100]  	[DEBUG]		[NotesManagementViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 6, Time since interaction: 3425.4541ms
[2025-08-09 14:27:35.111]  	[DEBUG]		[NotesManagementViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:27:35.132]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 1, Time since interaction: 5638.1657ms
[2025-08-09 14:27:35.141]  	[DEBUG]		[DuplicateClientDetectionDialogViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:27:35.270]  	[DEBUG]		[CustomWindowChromeViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 3, Time since interaction: 15807.9732ms
[2025-08-09 14:27:35.279]  	[DEBUG]		[CustomWindowChromeViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:27:35.290]  	[DEBUG]		[NPersonalViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 1, Time since interaction: 9186.9682ms
[2025-08-09 14:27:35.297]  	[DEBUG]		[NPersonalViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:27:35.315]  	[DEBUG]		[PersonalInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 1, Time since interaction: 9251.9151ms
[2025-08-09 14:27:35.325]  	[DEBUG]		[PersonalInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:27:35.332]  	[DEBUG]		[ContactInformationViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 0, Time since interaction: 15458.6528ms
[2025-08-09 14:27:35.351]  	[DEBUG]		[ContactInformationViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
[2025-08-09 14:27:35.361]  	[DEBUG]		[NewClientViewModel]	UI state changed from Idle to Background - Focus: False, Notifications/sec: 7, Time since interaction: 3569.3834ms
[2025-08-09 14:27:35.367]  	[DEBUG]		[NewClientViewModel]	Batching strategy changed from Balanced to Conservative based on UI state: Background
