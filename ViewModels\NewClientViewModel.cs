using System.Linq;
using System.Windows.Input;
using UFU2.Commands;
using UFU2.Common;
using UFU2.Models;
using UFU2.Services;

namespace UFU2.ViewModels
{
    /// <summary>
    /// Refactored ViewModel for the NewClientView providing MVVM data binding and command handling.
    /// Manages client creation workflow with loading states, validation, and activity management.
    /// Now uses composition with smaller, focused ViewModels for better maintainability.
    /// </summary>
    public class NewClientViewModel : BaseViewModel
    {
        #region Private Fields

        private bool _isLoading;
        private bool _canSave;

        // Database services for client management
        private ClientDatabaseService? _clientDatabaseService;
        private UIDGenerationService? _uidGenerationService;
        private ClientValidationService? _clientValidationService;
        private ClientFolderManagementService? _clientFolderManagementService;

        // State management for existing client editing
        private bool _isEditingExistingClient = false;
        private string? _existingClientUid = null;
        private DuplicateClientData? _originalClientData = null;

        #endregion

        #region Component ViewModels

        /// <summary>
        /// Gets the personal information management component.
        /// Handles client's basic details including names, birth information, gender, address, and profile image.
        /// </summary>
        public PersonalInformationViewModel PersonalInfo { get; }

        /// <summary>
        /// Gets the contact information management component.
        /// Handles phone numbers and other contact methods.
        /// </summary>
        public ContactInformationViewModel ContactInfo { get; }

        /// <summary>
        /// Gets the activity management component.
        /// Handles the collection of activities (Commercial, Craft, Professional), including adding, editing, and removing activities.
        /// </summary>
        public ActivityManagementViewModel ActivityManagement { get; }

        /// <summary>
        /// Gets the notes management component.
        /// Handles the collection of notes associated with the client.
        /// </summary>
        public NotesManagementViewModel NotesManagement { get; }

        #endregion

        #region Events and Delegates

        /// <summary>
        /// Delegate for requesting data collection from views before save operations.
        /// </summary>
        public Action? RequestDataCollection { get; set; }

        /// <summary>
        /// Event raised when the edit status command is executed.
        /// The view should handle this event to open the ActivityStatusUpdateDialog.
        /// </summary>
        public event Action? EditStatusRequested;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets a value indicating whether the view is in loading state
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                if (SetProperty(ref _isLoading, value))
                {
                    // Update CanSave when loading state changes
                    UpdateCanSave();
                }
            }
        }

        /// <summary>
        /// Gets or sets a value indicating whether the save operation can be executed
        /// </summary>
        public bool CanSave
        {
            get => _canSave;
            set => SetProperty(ref _canSave, value);
        }

        /// <summary>
        /// Gets or sets the currently selected activity type.
        /// Delegates to the ActivityManagement component.
        /// </summary>
        public string SelectedActivityType
        {
            get => ActivityManagement.SelectedActivityType;
            set => ActivityManagement.SelectedActivityType = value;
        }

        /// <summary>
        /// Gets or sets a value indicating whether the alternate Activity UID format is enabled.
        /// Delegates to the ActivityManagement component.
        /// </summary>
        public bool ActivityIdFormatEnabled
        {
            get => ActivityManagement.ActivityIdFormatEnabled;
            set => ActivityManagement.ActivityIdFormatEnabled = value;
        }

        /// <summary>
        /// Gets the current activity model containing all activity-related data for the selected tab.
        /// Delegates to the ActivityManagement component.
        /// </summary>
        public ActivityModel CurrentActivity => ActivityManagement.CurrentActivity;

        /// <summary>
        /// Gets the current multiple activities collection for the selected tab.
        /// Delegates to the ActivityManagement component.
        /// </summary>
        public List<ActivityTypeBaseModel> CurrentMultipleActivities => ActivityManagement.CurrentMultipleActivities;

        /// <summary>
        /// Gets the current file check states model containing all file check completion status for the selected tab.
        /// Delegates to the ActivityManagement component.
        /// </summary>
        public FileCheckStatesModel CurrentFileCheckStates => ActivityManagement.CurrentFileCheckStates;

        /// <summary>
        /// Gets or sets the French name (Latin characters) for the client.
        /// Delegates to the PersonalInfo component.
        /// </summary>
        public string NameFr
        {
            get => PersonalInfo.NameFr;
            set
            {
                PersonalInfo.NameFr = value;
                UpdateCanSave();
            }
        }

        /// <summary>
        /// Gets or sets the Arabic name for the client.
        /// Delegates to the PersonalInfo component.
        /// </summary>
        public string NameAr
        {
            get => PersonalInfo.NameAr;
            set => PersonalInfo.NameAr = value;
        }

        /// <summary>
        /// Gets or sets the birth date in DD/MM/YYYY format.
        /// Delegates to the PersonalInfo component.
        /// </summary>
        public string BirthDate
        {
            get => PersonalInfo.BirthDate;
            set => PersonalInfo.BirthDate = value;
        }

        /// <summary>
        /// Gets or sets the birth place.
        /// Delegates to the PersonalInfo component.
        /// </summary>
        public string BirthPlace
        {
            get => PersonalInfo.BirthPlace;
            set => PersonalInfo.BirthPlace = value;
        }

        /// <summary>
        /// Gets or sets the gender.
        /// Delegates to the PersonalInfo component.
        /// </summary>
        public int Gender
        {
            get => PersonalInfo.Gender;
            set => PersonalInfo.Gender = value;
        }

        /// <summary>
        /// Gets or sets the address.
        /// Delegates to the PersonalInfo component.
        /// </summary>
        public string Address
        {
            get => PersonalInfo.Address;
            set => PersonalInfo.Address = value;
        }

        /// <summary>
        /// Gets or sets the national ID number.
        /// Delegates to the PersonalInfo component.
        /// </summary>
        public string NationalId
        {
            get => PersonalInfo.NationalId;
            set => PersonalInfo.NationalId = value;
        }

        /// <summary>
        /// Gets the phone numbers collection for personal information.
        /// Delegates to the ContactInfo component.
        /// </summary>
        public PhoneNumbersCollectionModel PhoneNumbers => ContactInfo.PhoneNumbers;

        /// <summary>
        /// Gets the notes collection for client notes management.
        /// Delegates to the NotesManagement component.
        /// </summary>
        public NotesCollectionModel Notes => NotesManagement.Notes;

        /// <summary>
        /// Gets the collection of CPI Wilayas (administrative provinces) for ComboBox binding.
        /// Delegates to the ActivityManagement component.
        /// </summary>
        public List<CpiWilaya> CpiWilayas => ActivityManagement.CpiWilayas;

        /// <summary>
        /// Gets the collection of CPI Dairas (administrative districts) for ComboBox binding.
        /// Delegates to the ActivityManagement component.
        /// </summary>
        public System.Collections.ObjectModel.ObservableCollection<CpiDaira> CpiDairas => ActivityManagement.CpiDairas;

        /// <summary>
        /// Gets or sets the selected CPI Wilaya for the current activity.
        /// Delegates to the ActivityManagement component.
        /// </summary>
        public CpiWilaya? SelectedCpiWilaya
        {
            get => ActivityManagement.SelectedCpiWilaya;
            set => ActivityManagement.SelectedCpiWilaya = value;
        }

        /// <summary>
        /// Gets or sets the selected CPI Daira for the current activity.
        /// Delegates to the ActivityManagement component.
        /// </summary>
        public CpiDaira? SelectedCpiDaira
        {
            get => ActivityManagement.SelectedCpiDaira;
            set => ActivityManagement.SelectedCpiDaira = value;
        }

        /// <summary>
        /// Gets a value indicating whether the current session is editing an existing client
        /// rather than creating a new one.
        /// </summary>
        public bool IsEditingExistingClient
        {
            get => _isEditingExistingClient;
            private set => SetProperty(ref _isEditingExistingClient, value);
        }

        /// <summary>
        /// Gets the UID of the existing client being edited, if any.
        /// </summary>
        public string? ExistingClientUid
        {
            get => _existingClientUid;
            private set => SetProperty(ref _existingClientUid, value);
        }

        /// <summary>
        /// Gets the display text for the notes card in NFileCheckView.
        /// Delegates to the NotesManagement component.
        /// </summary>
        public string NotesDisplayText => NotesManagement.NotesDisplayText;

        /// <summary>
        /// Gets or sets the selected G12 payment years for the current activity tab.
        /// Delegates to the ActivityManagement component.
        /// </summary>
        public List<int> G12SelectedYears
        {
            get => ActivityManagement.G12SelectedYears;
            set => ActivityManagement.G12SelectedYears = value;
        }

        /// <summary>
        /// Gets or sets the selected BIS payment years for the current activity tab.
        /// Delegates to the ActivityManagement component.
        /// </summary>
        public List<int> BISSelectedYears
        {
            get => ActivityManagement.BISSelectedYears;
            set => ActivityManagement.BISSelectedYears = value;
        }

        /// <summary>
        /// Gets the display text for G12 payment years for the current activity tab.
        /// Delegates to the ActivityManagement component.
        /// </summary>
        public string G12DisplayText => ActivityManagement.G12DisplayText;

        /// <summary>
        /// Gets the display text for BIS payment years for the current activity tab.
        /// Delegates to the ActivityManagement component.
        /// </summary>
        public string BISDisplayText => ActivityManagement.BISDisplayText;

        /// <summary>
        /// Gets or sets the profile image for the client.
        /// Delegates to the PersonalInfo component.
        /// </summary>
        public System.Windows.Media.Imaging.BitmapSource? ProfileImage
        {
            get => PersonalInfo.ProfileImage;
            set => PersonalInfo.ProfileImage = value;
        }

        #endregion

        #region Commands

        /// <summary>
        /// Command to save the client data.
        /// </summary>
        public ICommand SaveClientCommand { get; }

        /// <summary>
        /// Command to close the dialog.
        /// </summary>
        public ICommand CloseCommand { get; }

        /// <summary>
        /// Command to switch between activity tabs.
        /// </summary>
        public ICommand SwitchActivityTabCommand { get; }

        /// <summary>
        /// Command to manage multiple activities.
        /// </summary>
        public ICommand ManageMultipleActivitiesCommand { get; }

        /// <summary>
        /// Command to show craft information.
        /// </summary>
        public ICommand ShowCraftInformationCommand { get; }

        /// <summary>
        /// Command to search craft types.
        /// </summary>
        public ICommand SearchCraftTypesCommand { get; }

        /// <summary>
        /// Command to edit activity status.
        /// </summary>
        public ICommand EditStatusCommand { get; }

        #endregion

        #region Constructor

        /// <summary>
        /// Initializes a new instance of the NewClientViewModel class.
        /// </summary>
        public NewClientViewModel()
        {
            // Initialize component ViewModels
            PersonalInfo = new PersonalInformationViewModel();
            ContactInfo = new ContactInformationViewModel();
            ActivityManagement = new ActivityManagementViewModel();
            NotesManagement = new NotesManagementViewModel();

            // Subscribe to component property changes that affect CanSave
            PersonalInfo.PropertyChanged += OnComponentPropertyChanged;
            ContactInfo.PropertyChanged += OnComponentPropertyChanged;
            ActivityManagement.PropertyChanged += OnComponentPropertyChanged;
            NotesManagement.PropertyChanged += OnComponentPropertyChanged;

            // Forward the EditStatusRequested event from ActivityManagement
            ActivityManagement.EditStatusRequested += () => EditStatusRequested?.Invoke();

            // Initialize services
            InitializeServices();

            // Initialize commands
            SaveClientCommand = new RelayCommand(
                execute: async () => await SaveClientAsync(),
                canExecute: () => CanSave && !IsLoading,
                commandName: "SaveClient"
            );

            CloseCommand = new RelayCommand(
                execute: CloseDialog,
                commandName: "Close"
            );

            SwitchActivityTabCommand = new RelayCommand(
                execute: (parameter) => SwitchActivityTab(parameter?.ToString()),
                commandName: "SwitchActivityTab"
            );

            ManageMultipleActivitiesCommand = new RelayCommand(
                execute: ManageMultipleActivities,
                commandName: "ManageMultipleActivities"
            );

            ShowCraftInformationCommand = new RelayCommand(
                execute: ShowCraftInformation,
                commandName: "ShowCraftInformation"
            );

            SearchCraftTypesCommand = new RelayCommand(
                execute: SearchCraftTypes,
                commandName: "SearchCraftTypes"
            );

            EditStatusCommand = new RelayCommand(
                execute: EditStatus,
                commandName: "EditStatus"
            );

            // Initialize CanSave state
            UpdateCanSave();

            LoggingService.LogDebug("NewClientViewModel initialized with component ViewModels", "NewClientViewModel");
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Refreshes the notes display text by triggering property change notification.
        /// Called from NFileCheckView when notes are updated through dialogs.
        /// </summary>
        public void RefreshNotesDisplay()
        {
            NotesManagement.RefreshNotesDisplay();
            OnPropertyChanged(nameof(NotesDisplayText));
        }

        /// <summary>
        /// Sets the editing state for an existing client loaded from duplicate detection.
        /// This method should be called when a user selects an existing client from the duplicate detection dialog.
        /// </summary>
        /// <param name="clientData">The original client data loaded from the database</param>
        public void SetEditingExistingClient(DuplicateClientData clientData)
        {
            if (clientData == null)
            {
                LoggingService.LogWarning("Cannot set editing state with null client data", "NewClientViewModel");
                return;
            }

            try
            {
                IsEditingExistingClient = true;
                ExistingClientUid = clientData.ClientUid;
                _originalClientData = clientData.Clone(); // Store original data for comparison

                // Load data into component ViewModels
                PersonalInfo.LoadFromClientData(clientData);

                LoggingService.LogInfo($"Set editing state for existing client: {clientData.ClientUid}", "NewClientViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error setting editing state for existing client: {ex.Message}", "NewClientViewModel");
                // Reset state on error
                ClearEditingState();
            }
        }

        /// <summary>
        /// Clears the editing state, returning to new client creation mode.
        /// </summary>
        public void ClearEditingState()
        {
            IsEditingExistingClient = false;
            ExistingClientUid = null;
            _originalClientData = null;
            LoggingService.LogDebug("Cleared editing state - returned to new client creation mode", "NewClientViewModel");
        }

        /// <summary>
        /// Sets the profile image for the client.
        /// </summary>
        /// <param name="image">The profile image</param>
        /// <param name="originalExtension">The original file extension</param>
        public void SetProfileImage(System.Windows.Media.Imaging.BitmapSource image, string originalExtension)
        {
            PersonalInfo.ProfileImage = image;
            PersonalInfo.ProfileImageOriginalExtension = originalExtension;
        }

        /// <summary>
        /// Auto-populates payment years based on activity start date.
        /// </summary>
        /// <param name="activityStartDate">The activity start date</param>
        public void AutoPopulatePaymentYears(string activityStartDate)
        {
            try
            {
                LoggingService.LogInfo($"Auto-populate payment years requested for date: {activityStartDate}", "NewClientViewModel");

                // Delegate to the ActivityManagement component
                ActivityManagement.AutoPopulatePaymentYears(activityStartDate);
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error auto-populating payment years: {ex.Message}", "NewClientViewModel");
            }
        }

        /// <summary>
        /// Clears payment years for the current activity.
        /// </summary>
        public void ClearPaymentYears()
        {
            try
            {
                // Delegate to the ActivityManagement component
                ActivityManagement.ClearPaymentYears();

                LoggingService.LogInfo("Payment years cleared", "NewClientViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error clearing payment years: {ex.Message}", "NewClientViewModel");
            }
        }

        /// <summary>
        /// Triggers validation for the current form data.
        /// </summary>
        public void TriggerValidation()
        {
            // Trigger validation across all components
            UpdateCanSave();
            LoggingService.LogDebug("Validation triggered", "NewClientViewModel");
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Initializes the database services.
        /// </summary>
        private void InitializeServices()
        {
            try
            {
                _clientDatabaseService = ServiceLocator.GetService<ClientDatabaseService>();
                _uidGenerationService = ServiceLocator.GetService<UIDGenerationService>();
                _clientValidationService = ServiceLocator.GetService<ClientValidationService>();
                _clientFolderManagementService = ServiceLocator.GetService<ClientFolderManagementService>();

                LoggingService.LogDebug("Services initialized successfully", "NewClientViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error initializing services: {ex.Message}", "NewClientViewModel");
            }
        }

        /// <summary>
        /// Handles property changes from component ViewModels.
        /// </summary>
        private void OnComponentPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
        {
            // Update CanSave when relevant properties change
            if (e.PropertyName == nameof(PersonalInformationViewModel.NameFr))
            {
                UpdateCanSave();
                OnPropertyChanged(nameof(NameFr));
            }
            else if (e.PropertyName == nameof(PersonalInformationViewModel.NameAr))
            {
                OnPropertyChanged(nameof(NameAr));
            }
            else if (e.PropertyName == nameof(PersonalInformationViewModel.BirthDate))
            {
                OnPropertyChanged(nameof(BirthDate));
            }
            else if (e.PropertyName == nameof(PersonalInformationViewModel.BirthPlace))
            {
                OnPropertyChanged(nameof(BirthPlace));
            }
            else if (e.PropertyName == nameof(PersonalInformationViewModel.Gender))
            {
                OnPropertyChanged(nameof(Gender));
            }
            else if (e.PropertyName == nameof(PersonalInformationViewModel.Address))
            {
                OnPropertyChanged(nameof(Address));
            }
            else if (e.PropertyName == nameof(PersonalInformationViewModel.NationalId))
            {
                OnPropertyChanged(nameof(NationalId));
            }
            else if (e.PropertyName == nameof(PersonalInformationViewModel.ProfileImage))
            {
                OnPropertyChanged(nameof(ProfileImage));
            }
            else if (e.PropertyName == nameof(ActivityManagementViewModel.SelectedActivityType))
            {
                OnPropertyChanged(nameof(SelectedActivityType));
                OnPropertyChanged(nameof(CurrentActivity));
                OnPropertyChanged(nameof(CurrentFileCheckStates));
                OnPropertyChanged(nameof(G12SelectedYears));
                OnPropertyChanged(nameof(BISSelectedYears));
                OnPropertyChanged(nameof(G12DisplayText));
                OnPropertyChanged(nameof(BISDisplayText));
            }
            else if (e.PropertyName == nameof(ActivityManagementViewModel.ActivityIdFormatEnabled))
            {
                OnPropertyChanged(nameof(ActivityIdFormatEnabled));
            }
            else if (e.PropertyName == nameof(ActivityManagementViewModel.SelectedCpiWilaya))
            {
                OnPropertyChanged(nameof(SelectedCpiWilaya));
            }
            else if (e.PropertyName == nameof(ActivityManagementViewModel.SelectedCpiDaira))
            {
                OnPropertyChanged(nameof(SelectedCpiDaira));
            }
            else if (e.PropertyName == nameof(ActivityManagementViewModel.G12DisplayText))
            {
                OnPropertyChanged(nameof(G12DisplayText));
            }
            else if (e.PropertyName == nameof(ActivityManagementViewModel.BISDisplayText))
            {
                OnPropertyChanged(nameof(BISDisplayText));
            }
            else if (e.PropertyName == nameof(NotesManagementViewModel.NotesDisplayText))
            {
                OnPropertyChanged(nameof(NotesDisplayText));
            }
        }

        /// <summary>
        /// Updates the CanSave property based on current validation state.
        /// </summary>
        private void UpdateCanSave()
        {
            try
            {
                bool canSave = !IsLoading && 
                              PersonalInfo.IsValid() && 
                              ContactInfo.IsValid() && 
                              ActivityManagement.IsValid() && 
                              NotesManagement.IsValid();

                CanSave = canSave;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating CanSave state: {ex.Message}", "NewClientViewModel");
                CanSave = false;
            }
        }

        /// <summary>
        /// Checks if the current form data has changes compared to the original client data.
        /// Only compares personal information fields that can be updated.
        /// </summary>
        /// <returns>True if there are changes, false if no changes detected</returns>
        private bool HasPersonalInfoChanges()
        {
            return PersonalInfo.HasChanges(_originalClientData);
        }

        /// <summary>
        /// Saves the client data to the database.
        /// </summary>
        private async Task SaveClientAsync()
        {
            if (IsLoading || !CanSave)
            {
                return;
            }

            try
            {
                IsLoading = true;

                // Request data collection from views
                RequestDataCollection?.Invoke();

                // Validate all components
                if (!PersonalInfo.IsValid() || !ContactInfo.IsValid() || 
                    !ActivityManagement.IsValid() || !NotesManagement.IsValid())
                {
                    LoggingService.LogWarning("Validation failed during save operation", "NewClientViewModel");
                    return;
                }

                // Create client data object from component ViewModels
                var clientData = await CreateClientDataFromComponentsAsync();

                // Save to database
                string clientUid;
                if (IsEditingExistingClient && !string.IsNullOrEmpty(ExistingClientUid))
                {
                    // Update existing client
                    clientUid = await UpdateExistingClientAsync(clientData);
                }
                else
                {
                    // Create new client
                    clientUid = await CreateNewClientAsync(clientData);
                }

                if (!string.IsNullOrEmpty(clientUid))
                {
                    LoggingService.LogInfo($"Client saved successfully: {clientUid}", "NewClientViewModel");

                    // Create client folder structure for new clients
                    if (!IsEditingExistingClient)
                    {
                        await CreateClientFolderStructureAsync(clientUid);
                    }

                    // Show success message
                    ErrorManager.ShowUserSuccessToast(
                        IsEditingExistingClient ? "تم تحديث بيانات العميل بنجاح" : "تم إنشاء العميل بنجاح",
                        "نجح الحفظ"
                    );

                    // Close the dialog
                    CloseDialog();
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error saving client: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء حفظ بيانات العميل",
                    "خطأ في الحفظ",
                    LogLevel.Error,
                    "NewClientViewModel");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Creates a ClientCreationData object from the component ViewModels.
        /// </summary>
        private async Task<ClientCreationData> CreateClientDataFromComponentsAsync()
        {
            // Convert PhoneNumbersCollectionModel to List<PhoneNumberData>
            var phoneNumbersList = new List<PhoneNumberData>();
            if (ContactInfo.PhoneNumbers?.PhoneNumbers != null)
            {
                foreach (var phoneModel in ContactInfo.PhoneNumbers.PhoneNumbers)
                {
                    phoneNumbersList.Add(new PhoneNumberData
                    {
                        PhoneNumber = phoneModel.PhoneNumber,
                        PhoneType = phoneModel.PhoneType.ToString(),
                        IsPrimary = phoneModel.IsPrimary
                    });
                }
            }

            // Convert NotesCollectionModel to List<NoteData>
            var notesList = new List<NoteData>();
            if (NotesManagement.Notes?.Notes != null)
            {
                foreach (var noteModel in NotesManagement.Notes.Notes)
                {
                    notesList.Add(new NoteData
                    {
                        Content = noteModel.Content,
                        CreatedDate = noteModel.FormattedCreatedDate,
                        Category = noteModel.Category
                    });
                }
            }

            // Create activities data from ActivityManagement component
            var activitiesData = await CreateActivitiesDataFromComponentAsync();

            return new ClientCreationData
            {
                NameFr = PersonalInfo.NameFr,
                NameAr = PersonalInfo.NameAr,
                BirthDate = PersonalInfo.BirthDate,
                BirthPlace = PersonalInfo.BirthPlace,
                Gender = PersonalInfo.Gender,
                Address = PersonalInfo.Address,
                NationalId = PersonalInfo.NationalId,
                PhoneNumbers = phoneNumbersList,
                Notes = notesList,
                Activities = activitiesData
            };
        }

        /// <summary>
        /// Creates activities data from the ActivityManagement component.
        /// </summary>
        private async Task<List<ActivityCreationData>> CreateActivitiesDataFromComponentAsync()
        {
            try
            {
                var activities = new List<ActivityCreationData>();

                // Always create a default activity for the currently active tab
                await CreateDefaultActivityForActiveTab(activities);

                LoggingService.LogDebug($"Created {activities.Count} activities from component data", "NewClientViewModel");
                return activities;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating activities data from component: {ex.Message}", "NewClientViewModel");
                return new List<ActivityCreationData>();
            }
        }

        /// <summary>
        /// Creates a default activity for the currently active tab.
        /// </summary>
        private async Task CreateDefaultActivityForActiveTab(List<ActivityCreationData> activities)
        {
            try
            {
                var currentActivity = ActivityManagement.CurrentActivity;
                var currentFileCheckStates = ActivityManagement.CurrentFileCheckStates;
                var selectedActivityType = ActivityManagement.SelectedActivityType;

                // Create activity data from current UI state
                var activityData = new ActivityCreationData
                {
                    ActivityType = selectedActivityType,
                    ActivityCode = currentActivity.ActivityCode,
                    ActivityDescription = currentActivity.ActivityDescription,
                    ActivityStatus = currentActivity.ActivityStatus,
                    ActivityStartDate = currentActivity.ActivityStartDate,
                    CommercialRegister = currentActivity.CommercialRegister,
                    ActivityLocation = currentActivity.ActivityLocation,
                    NifNumber = currentActivity.NifNumber,
                    NisNumber = currentActivity.NisNumber,
                    ArtNumber = currentActivity.ArtNumber,
                    CpiDaira = currentActivity.CpiDaira,
                    CpiWilaya = currentActivity.CpiWilaya,
                    ActivityUpdateDate = currentActivity.ActivityUpdateDate,
                    ActivityUpdateNote = currentActivity.ActivityUpdateNote
                };

                // Add activity codes for commercial activities
                if ((selectedActivityType == "MainCommercial" || selectedActivityType == "SecondaryCommercial") &&
                    ActivityManagement.CurrentMultipleActivities.Count > 0)
                {
                    activityData.ActivityCodes = ActivityManagement.CurrentMultipleActivities
                        .Where(a => int.TryParse(a.Code, out _))
                        .Select(a => int.Parse(a.Code))
                        .ToList();
                }

                // Add craft code for craft activities
                if (selectedActivityType == "Craft" && !string.IsNullOrWhiteSpace(currentActivity.ActivityCode))
                {
                    activityData.CraftCode = currentActivity.ActivityCode;
                }

                // Add file check states - convert from FileCheckStatesModel to Dictionary
                var fileCheckData = new FileCheckStatesData
                {
                    CommercialRegisterCheck = currentFileCheckStates.CommercialRegisterCheck,
                    TaxCertificateCheck = currentFileCheckStates.TaxCertificateCheck,
                    SocialSecurityCheck = currentFileCheckStates.SocialSecurityCheck,
                    MunicipalityCheck = currentFileCheckStates.MunicipalityCheck,
                    ChamberCommerceCheck = currentFileCheckStates.ChamberCommerceCheck,
                    InsuranceCheck = currentFileCheckStates.InsuranceCheck,
                    BankStatementCheck = currentFileCheckStates.BankStatementCheck,
                    RentalContractCheck = currentFileCheckStates.RentalContractCheck,
                    UtilityBillCheck = currentFileCheckStates.UtilityBillCheck,
                    IdentityDocumentCheck = currentFileCheckStates.IdentityDocumentCheck,
                    PhotoCheck = currentFileCheckStates.PhotoCheck,
                    OtherDocumentsCheck = currentFileCheckStates.OtherDocumentsCheck,
                    CraftCardCheck = currentFileCheckStates.CraftCardCheck,
                    ProfessionalLicenseCheck = currentFileCheckStates.ProfessionalLicenseCheck
                };
                activityData.FileCheckStates = fileCheckData.ToDictionary(selectedActivityType);

                // Add payment years data
                activityData.G12SelectedYears = ActivityManagement.G12SelectedYears?.ToList() ?? new List<int>();
                activityData.BISSelectedYears = ActivityManagement.BISSelectedYears?.ToList() ?? new List<int>();

                activities.Add(activityData);

                LoggingService.LogDebug($"Created default activity for {selectedActivityType} tab", "NewClientViewModel");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating default activity for active tab: {ex.Message}", "NewClientViewModel");
            }
        }

        /// <summary>
        /// Creates a new client in the database.
        /// </summary>
        private async Task<string> CreateNewClientAsync(ClientCreationData clientData)
        {
            if (_clientDatabaseService == null)
            {
                throw new InvalidOperationException("ClientDatabaseService is not available");
            }

            return await _clientDatabaseService.CreateClientAsync(clientData);
        }

        /// <summary>
        /// Updates an existing client in the database.
        /// </summary>
        private async Task<string> UpdateExistingClientAsync(ClientCreationData clientData)
        {
            if (_clientDatabaseService == null || string.IsNullOrEmpty(ExistingClientUid))
            {
                throw new InvalidOperationException("ClientDatabaseService is not available or ExistingClientUid is null");
            }

            // Convert ClientCreationData to ClientUpdateData
            var updateData = new ClientUpdateData
            {
                NameAr = clientData.NameAr,
                BirthDate = clientData.BirthDate,
                BirthPlace = clientData.BirthPlace,
                Gender = clientData.Gender,
                Address = clientData.Address,
                NationalId = clientData.NationalId
            };

            await _clientDatabaseService.UpdateClientAsync(ExistingClientUid, updateData);
            return ExistingClientUid;
        }

        /// <summary>
        /// Creates the client folder structure after successful client creation.
        /// Generates the activity UID and calls the ClientFolderManagementService to create the folder structure.
        /// </summary>
        /// <param name="clientUID">The client UID that was created</param>
        private async Task CreateClientFolderStructureAsync(string clientUID)
        {
            try
            {
                // Check if folder management service is available
                if (_clientFolderManagementService == null)
                {
                    LoggingService.LogWarning("ClientFolderManagementService not available - skipping folder creation", "NewClientViewModel");
                    return;
                }

                // Get client name for folder creation
                string clientNameFr = PersonalInfo.NameFr?.Trim() ?? "Unknown";

                // Generate activity UID (first activity for this client)
                string activityUID = ActivityIdFormatEnabled
                    ? $"{clientUID}_Act1s"
                    : $"{clientUID}_Act1";

                LoggingService.LogInfo($"Creating folder structure for client {clientUID} with activity {activityUID}", "NewClientViewModel");

                // Create the folder structure
                bool folderCreated = await _clientFolderManagementService.CreateClientFolderStructureAsync(
                    clientUID, clientNameFr, activityUID);

                if (folderCreated)
                {
                    LoggingService.LogInfo($"Successfully created folder structure for client {clientUID}", "NewClientViewModel");

                    // Copy profile image to client folder if available
                    if (PersonalInfo.HasProfileImage && PersonalInfo.ProfileImage != null)
                    {
                        LoggingService.LogInfo($"Copying profile image to client folder for {clientUID}", "NewClientViewModel");

                        bool imageCopied = await _clientFolderManagementService.CopyProfileImageToClientFolderAsync(
                            clientUID, clientNameFr, PersonalInfo.ProfileImage, PersonalInfo.ProfileImageOriginalExtension ?? "jpg");

                        if (imageCopied)
                        {
                            LoggingService.LogInfo($"Profile image copied successfully for client {clientUID}", "NewClientViewModel");
                        }
                        else
                        {
                            LoggingService.LogWarning($"Failed to copy profile image for client {clientUID}", "NewClientViewModel");
                            // Note: Error messages are already shown by the ClientFolderManagementService
                            // This is not a critical failure - the client and folder structure were created successfully
                        }
                    }
                    else
                    {
                        LoggingService.LogDebug($"No profile image to copy for client {clientUID}", "NewClientViewModel");
                    }
                }
                else
                {
                    LoggingService.LogWarning($"Failed to create folder structure for client {clientUID}", "NewClientViewModel");
                    // Note: Error messages are already shown by the ClientFolderManagementService
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Unexpected error creating folder structure for client {clientUID}: {ex.Message}", "NewClientViewModel");
                // Don't throw - folder creation failure shouldn't prevent client creation success
            }
        }

        /// <summary>
        /// Switches to the specified activity tab.
        /// </summary>
        /// <param name="activityType">The activity type to switch to</param>
        private void SwitchActivityTab(string? activityType)
        {
            try
            {
                if (!string.IsNullOrEmpty(activityType))
                {
                    SelectedActivityType = activityType;
                    LoggingService.LogInfo($"Switched to activity tab: {activityType}", "NewClientViewModel");
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error switching activity tab: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء تبديل نوع النشاط",
                    "خطأ في التبديل",
                    LogLevel.Error,
                    "NewClientViewModel");
            }
        }

        /// <summary>
        /// Opens the multiple activities management dialog.
        /// </summary>
        private async void ManageMultipleActivities()
        {
            try
            {
                LoggingService.LogInfo("Multiple activities management requested", "NewClientViewModel");

                // Delegate to ActivityManagement component
                await ActivityManagement.ManageMultipleActivitiesAsync();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error opening multiple activities management: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء فتح إدارة الأنشطة المتعددة",
                    "خطأ في إدارة الأنشطة",
                    LogLevel.Error,
                    "NewClientViewModel");
            }
        }

        /// <summary>
        /// Shows craft information dialog.
        /// </summary>
        private async void ShowCraftInformation()
        {
            try
            {
                LoggingService.LogInfo("Craft information dialog requested", "NewClientViewModel");

                // Delegate to ActivityManagement component
                await ActivityManagement.ShowCraftInformationAsync();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error showing craft information: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء عرض معلومات الحرفة",
                    "خطأ في عرض المعلومات",
                    LogLevel.Error,
                    "NewClientViewModel");
            }
        }

        /// <summary>
        /// Opens the craft types search dialog.
        /// </summary>
        private async void SearchCraftTypes()
        {
            try
            {
                LoggingService.LogInfo("Craft types search dialog requested", "NewClientViewModel");

                // Delegate to ActivityManagement component
                await ActivityManagement.SearchCraftTypesAsync();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error opening craft types search: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء فتح البحث عن أنواع الحرف",
                    "خطأ في البحث",
                    LogLevel.Error,
                    "NewClientViewModel");
            }
        }

        /// <summary>
        /// Opens the activity status edit dialog.
        /// </summary>
        private void EditStatus()
        {
            try
            {
                LoggingService.LogInfo("Activity status edit requested", "NewClientViewModel");
                EditStatusRequested?.Invoke();
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error editing activity status: {ex.Message}", "NewClientViewModel");
                ErrorManager.HandleErrorToast(ex,
                    "حدث خطأ أثناء تعديل حالة النشاط",
                    "خطأ في تعديل الحالة",
                    LogLevel.Error,
                    "NewClientViewModel");
            }
        }

        /// <summary>
        /// Closes the dialog.
        /// </summary>
        private void CloseDialog()
        {
            try
            {
                // Clear all component data
                PersonalInfo.Clear();
                ContactInfo.Clear();
                ActivityManagement.Clear();
                NotesManagement.Clear();

                // Clear editing state
                ClearEditingState();

                LoggingService.LogInfo("Dialog closed and data cleared", "NewClientViewModel");

                // Close the dialog using MaterialDesign DialogHost
                try
                {
                    // Use the static Close method with the RootDialog identifier
                    MaterialDesignThemes.Wpf.DialogHost.Close("RootDialog", true);
                    LoggingService.LogDebug("Successfully closed RootDialog with success result", "NewClientViewModel");
                }
                catch (Exception closeEx)
                {
                    LoggingService.LogError($"Error closing RootDialog: {closeEx.Message}", "NewClientViewModel");
                    // Try alternative close method using command
                    try
                    {
                        MaterialDesignThemes.Wpf.DialogHost.CloseDialogCommand.Execute(true, null);
                        LoggingService.LogDebug("Successfully executed fallback dialog close command (success)", "NewClientViewModel");
                    }
                    catch (Exception fallbackEx)
                    {
                        LoggingService.LogError($"Error with fallback dialog close: {fallbackEx.Message}", "NewClientViewModel");
                    }
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error closing dialog: {ex.Message}", "NewClientViewModel");
            }
        }

        #endregion

        #region IDisposable Implementation

        /// <summary>
        /// Disposes of the ViewModel resources.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                // Unsubscribe from component events
                if (PersonalInfo != null)
                {
                    PersonalInfo.PropertyChanged -= OnComponentPropertyChanged;
                    PersonalInfo.Dispose();
                }

                if (ContactInfo != null)
                {
                    ContactInfo.PropertyChanged -= OnComponentPropertyChanged;
                    ContactInfo.Dispose();
                }

                if (ActivityManagement != null)
                {
                    ActivityManagement.PropertyChanged -= OnComponentPropertyChanged;
                    ActivityManagement.EditStatusRequested -= () => EditStatusRequested?.Invoke();
                    ActivityManagement.Dispose();
                }

                if (NotesManagement != null)
                {
                    NotesManagement.PropertyChanged -= OnComponentPropertyChanged;
                    NotesManagement.Dispose();
                }

                LoggingService.LogDebug("NewClientViewModel disposed", "NewClientViewModel");
            }

            base.Dispose(disposing);
        }

        #endregion
    }
}